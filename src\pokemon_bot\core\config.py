"""Configuration management for Pokemon <PERSON>."""

import os
import yaml
from pathlib import Path
from typing import Any, Dict, Optional, Union
from dataclasses import dataclass, field
from dotenv import load_dotenv

from .exceptions import ConfigurationError


@dataclass
class ADBConfig:
    """ADB connection configuration."""
    host: str = "localhost"
    port: int = 5037
    connection_timeout: int = 30
    command_timeout: int = 10


@dataclass
class ImageDetectionConfig:
    """Image detection configuration."""
    threshold: float = 0.9
    resize_factor: float = 0.5
    template_matching_method: str = "TM_CCOEFF_NORMED"
    cache_enabled: bool = True
    cache_size: int = 100


@dataclass
class OCRConfig:
    """OCR configuration."""
    tesseract_cmd: str = "tesseract"
    config: str = "--oem 3 --psm 6"
    preprocessing: Dict[str, Any] = field(default_factory=lambda: {
        "resize_factor": 0.5,
        "grayscale": True,
        "threshold": 128
    })


@dataclass
class TelegramConfig:
    """Telegram bot configuration."""
    bot_token: str = ""
    chat_id: str = ""
    enabled: bool = True
    timeout: int = 30


@dataclass
class MongoDBConfig:
    """MongoDB configuration."""
    connection_string: str = ""
    database: str = "pokebot"
    collections: Dict[str, str] = field(default_factory=lambda: {
        "accounts": "cuentas",
        "logs": "logs",
        "metrics": "metrics"
    })
    connection_timeout: int = 30000
    server_selection_timeout: int = 5000


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: Dict[str, Any] = field(default_factory=lambda: {
        "enabled": True,
        "path": "logs/pokemon_bot.log",
        "max_size_mb": 10,
        "backup_count": 5
    })
    console: Dict[str, Any] = field(default_factory=lambda: {
        "enabled": True,
        "colored": True
    })


class Config:
    """Main configuration class that loads and manages all settings."""
    
    def __init__(self, config_dir: Optional[Union[str, Path]] = None):
        """Initialize configuration.
        
        Args:
            config_dir: Directory containing configuration files.
                       Defaults to 'config' in the project root.
        """
        self.config_dir = Path(config_dir) if config_dir else self._get_default_config_dir()
        self._config_data: Dict[str, Any] = {}
        self._load_configuration()
        
        # Initialize configuration sections
        self.adb = ADBConfig(**self._get_section("adb", {}))
        self.image_detection = ImageDetectionConfig(**self._get_section("image_detection", {}))
        self.ocr = OCRConfig(**self._get_section("ocr", {}))
        self.telegram = TelegramConfig(**self._get_section("telegram", {}))
        self.mongodb = MongoDBConfig(**self._get_section("mongodb", {}))
        self.logging = LoggingConfig(**self._get_section("logging", {}))
        
        # Apply environment variable overrides
        self._apply_env_overrides()
        
        # Validate critical configuration
        self._validate_config()
    
    def _get_default_config_dir(self) -> Path:
        """Get the default configuration directory."""
        # Try to find config directory relative to the package
        current_dir = Path(__file__).parent.parent.parent.parent
        config_dir = current_dir / "config"
        
        if not config_dir.exists():
            # Fallback to current working directory
            config_dir = Path.cwd() / "config"
        
        return config_dir
    
    def _load_configuration(self) -> None:
        """Load configuration from YAML files."""
        # Load default configuration
        default_config_path = self.config_dir / "default.yaml"
        if default_config_path.exists():
            with open(default_config_path, 'r', encoding='utf-8') as f:
                self._config_data = yaml.safe_load(f) or {}
        else:
            raise ConfigurationError(f"Default configuration file not found: {default_config_path}")
        
        # Load local configuration overrides
        local_config_path = self.config_dir / "local.yaml"
        if local_config_path.exists():
            with open(local_config_path, 'r', encoding='utf-8') as f:
                local_config = yaml.safe_load(f) or {}
                self._merge_config(self._config_data, local_config)
        
        # Load environment variables from .env file
        env_file = Path.cwd() / ".env"
        if env_file.exists():
            load_dotenv(env_file)
    
    def _merge_config(self, base: Dict[str, Any], override: Dict[str, Any]) -> None:
        """Recursively merge configuration dictionaries."""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def _get_section(self, section_name: str, default: Dict[str, Any]) -> Dict[str, Any]:
        """Get a configuration section with fallback to default."""
        return self._config_data.get(section_name, default)
    
    def _apply_env_overrides(self) -> None:
        """Apply environment variable overrides to configuration."""
        # Telegram configuration
        if os.getenv("TELEGRAM_BOT_TOKEN"):
            self.telegram.bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        if os.getenv("TELEGRAM_CHAT_ID"):
            self.telegram.chat_id = os.getenv("TELEGRAM_CHAT_ID")
        
        # MongoDB configuration
        if os.getenv("MONGODB_CONNECTION_STRING"):
            self.mongodb.connection_string = os.getenv("MONGODB_CONNECTION_STRING")
        
        # OCR configuration
        if os.getenv("TESSERACT_CMD"):
            self.ocr.tesseract_cmd = os.getenv("TESSERACT_CMD")
        
        # Logging configuration
        if os.getenv("LOG_LEVEL"):
            self.logging.level = os.getenv("LOG_LEVEL")
        
        # Debug mode
        if os.getenv("DEBUG", "").lower() in ("true", "1", "yes"):
            self.logging.level = "DEBUG"
    
    def _validate_config(self) -> None:
        """Validate critical configuration settings."""
        if self.telegram.enabled and not self.telegram.bot_token:
            raise ConfigurationError("Telegram bot token is required when Telegram is enabled")
        
        if not self.mongodb.connection_string:
            raise ConfigurationError("MongoDB connection string is required")
        
        if not Path(self.ocr.tesseract_cmd).exists() and self.ocr.tesseract_cmd != "tesseract":
            # Only validate path if it's not the default "tesseract" command
            if not any(Path(p) / self.ocr.tesseract_cmd for p in os.environ.get("PATH", "").split(os.pathsep)):
                raise ConfigurationError(f"Tesseract executable not found: {self.ocr.tesseract_cmd}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get a configuration value by dot notation key."""
        keys = key.split('.')
        value = self._config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """Set a configuration value by dot notation key."""
        keys = key.split('.')
        config = self._config_data
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def to_dict(self) -> Dict[str, Any]:
        """Return the complete configuration as a dictionary."""
        return self._config_data.copy()


# Global configuration instance
_config: Optional[Config] = None


def get_config() -> Config:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = Config()
    return _config


def reload_config(config_dir: Optional[Union[str, Path]] = None) -> Config:
    """Reload the global configuration."""
    global _config
    _config = Config(config_dir)
    return _config
