# Pokemon Bot Configuration
# This is the default configuration file. Override values in local.yaml or via environment variables.

# Application settings
app:
  name: "Pokemon Bot"
  version: "2.0.0"
  debug: false
  log_level: "INFO"

# ADB Configuration
adb:
  host: "localhost"
  port: 5037
  connection_timeout: 30
  command_timeout: 10

# Image Detection Settings
image_detection:
  threshold: 0.9
  resize_factor: 0.5
  template_matching_method: "TM_CCOEFF_NORMED"
  cache_enabled: true
  cache_size: 100

# OCR Configuration
ocr:
  tesseract_cmd: "tesseract"  # Will be overridden by environment-specific config
  config: "--oem 3 --psm 6"
  preprocessing:
    resize_factor: 0.5
    grayscale: true
    threshold: 128

# Screenshot Settings
screenshot:
  format: "PNG"
  quality: 85
  temp_dir: "/tmp"
  cleanup_after: 3600  # seconds

# Telegram Bot Configuration (will be overridden by environment variables)
telegram:
  bot_token: ""  # Set via TELEGRAM_BOT_TOKEN environment variable
  chat_id: ""    # Set via TELEGRAM_CHAT_ID environment variable
  enabled: true
  timeout: 30

# MongoDB Configuration (will be overridden by environment variables)
mongodb:
  connection_string: ""  # Set via MONGODB_CONNECTION_STRING environment variable
  database: "pokebot"
  collections:
    accounts: "cuentas"
    logs: "logs"
    metrics: "metrics"
  connection_timeout: 30000
  server_selection_timeout: 5000

# Adventure Settings
adventures:
  galar:
    enabled: true
    max_retries: 3
    timeout: 300
  dynamax:
    enabled: true
    max_retries: 3
    timeout: 600

# Device Management
devices:
  max_concurrent: 5
  health_check_interval: 60
  reconnect_attempts: 3
  reconnect_delay: 5

# Performance Settings
performance:
  max_workers: 4
  queue_size: 100
  memory_limit_mb: 512
  cpu_limit_percent: 80

# Retry Configuration
retry:
  max_attempts: 3
  backoff_factor: 2
  max_delay: 60

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file:
    enabled: true
    path: "logs/pokemon_bot.log"
    max_size_mb: 10
    backup_count: 5
  console:
    enabled: true
    colored: true

# Security Settings
security:
  encrypt_credentials: true
  session_timeout: 3600
  max_login_attempts: 3
