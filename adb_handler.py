# -*- coding: utf-8 -*-
"""
Created on Sun Dec 15 01:22:31 2024

@author: andre
"""
import numpy as np
import cv2
from ppadb.client import Client
from config import ADB_HOST, ADB_PORT
class ADBHandler:
    def __init__(self, device_id):
        self.adb = Client(host=ADB_HOST, port=ADB_PORT)
        self.device = self.adb.device(device_id)
        if not self.device:
             raise Exception(f"device with {device_id}  not found")
        self.device_id = device_id

    def shell(self, command):
        return self.device.shell(command)

    def pull(self, remote_path, local_path):
       return self.device.pull(remote_path, local_path)

    def push(self, local_path, remote_path):
       return self.device.push(local_path, remote_path)

    def screencap(self):
        return self.device.screencap()
    
    def get_cv2_image(self):
          image = self.device.screencap()
          screenshot_array = cv2.imdecode(np.frombuffer(image, dtype=np.uint8), cv2.IMREAD_COLOR)
          return screenshot_array