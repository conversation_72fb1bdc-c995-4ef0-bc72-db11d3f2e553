"""Adventure automation service."""

import time
from typing import Dict, Any, Optional
from ..core.logging import get_logger
from ..core.exceptions import AdventureError

logger = get_logger(__name__)


class AdventureService:
    """Service for managing adventure automation."""
    
    def __init__(self, bot_instance):
        """Initialize adventure service.
        
        Args:
            bot_instance: Reference to the main bot instance
        """
        self.bot = bot_instance
        self.config = bot_instance.config
    
    def run_adventure(self, adventure_type: str, username: str, **kwargs) -> bool:
        """Run a specific adventure.
        
        Args:
            adventure_type: Type of adventure to run
            username: Username for logging
            **kwargs: Adventure-specific parameters
            
        Returns:
            True if adventure completed successfully
        """
        start_time = time.time()
        
        try:
            logger.log_adventure_start(adventure_type, username, self.bot.device_id)
            
            # Send start notification
            if self.bot.telegram:
                self.bot.telegram.send_adventure_start(username, adventure_type, self.bot.device_id)
            
            # Run the specific adventure
            if adventure_type.lower() == "galar":
                success = self._run_galar_adventure(username, **kwargs)
            elif adventure_type.lower() == "dynamax":
                success = self._run_dynamax_adventure(username, **kwargs)
            else:
                raise AdventureError(f"Unknown adventure type: {adventure_type}")
            
            duration = time.time() - start_time
            
            if success:
                logger.log_adventure_complete(adventure_type, username, self.bot.device_id, duration)
                
                # Send completion notification
                if self.bot.telegram:
                    self.bot.telegram.send_adventure_complete(
                        username, adventure_type, self.bot.device_id, duration, True
                    )
                
                # Update user status
                if self.bot.user_service:
                    self.bot.user_service.mark_user_completed(username, adventure_type, duration)
            else:
                logger.log_adventure_error(adventure_type, username, self.bot.device_id, 
                                         AdventureError("Adventure failed"))
                
                # Send failure notification
                if self.bot.telegram:
                    self.bot.telegram.send_adventure_complete(
                        username, adventure_type, self.bot.device_id, duration, False
                    )
            
            return success
            
        except Exception as e:
            duration = time.time() - start_time
            logger.log_adventure_error(adventure_type, username, self.bot.device_id, e)
            
            # Send error notification
            if self.bot.telegram:
                self.bot.telegram.send_error_notification(str(e), username, self.bot.device_id)
            
            # Update user status
            if self.bot.user_service:
                self.bot.user_service.mark_user_failed(username, adventure_type, str(e))
            
            return False
    
    def _run_galar_adventure(self, username: str, **kwargs) -> bool:
        """Run Galar adventure (simplified version of original logic).
        
        Args:
            username: Username for logging
            **kwargs: Additional parameters
            
        Returns:
            True if adventure completed successfully
        """
        try:
            logger.info(f"Starting Galar adventure for {username}")
            
            # Basic adventure simulation - in real implementation, this would contain
            # the actual game automation logic from the original galar_adventure2 function
            
            # Step 1: Navigate to menu
            self._navigate_to_menu()
            
            # Step 2: Heal Pokemon if needed
            self._heal_pokemon()
            
            # Step 3: Navigate to adventure
            if not self._navigate_to_galar_adventure():
                return False
            
            # Step 4: Execute adventure
            success = self._execute_galar_adventure()
            
            # Step 5: Handle results
            if success:
                self._handle_adventure_completion(username)
            
            return success
            
        except Exception as e:
            logger.error(f"Galar adventure failed for {username}: {e}")
            return False
    
    def _run_dynamax_adventure(self, username: str, **kwargs) -> bool:
        """Run Dynamax adventure.
        
        Args:
            username: Username for logging
            **kwargs: Additional parameters
            
        Returns:
            True if adventure completed successfully
        """
        try:
            logger.info(f"Starting Dynamax adventure for {username}")
            
            # Simplified Dynamax adventure logic
            # In real implementation, this would contain the actual automation
            
            # Navigate to Dynamax adventure
            self._navigate_to_menu()
            
            # Search for Dynamax adventure
            if not self._find_dynamax_adventure():
                return False
            
            # Execute adventure
            success = self._execute_dynamax_adventure()
            
            if success:
                self._handle_adventure_completion(username)
            
            return success
            
        except Exception as e:
            logger.error(f"Dynamax adventure failed for {username}: {e}")
            return False
    
    def _navigate_to_menu(self) -> None:
        """Navigate to main menu."""
        logger.debug("Navigating to main menu")

        # Press back button multiple times to ensure we're at main screen
        for _ in range(3):
            self.bot.adb.back()
            time.sleep(1)

        # Try multiple menu-related images
        menu_images = ["menu.png", "adventure.png", "pkbss10.png"]

        for menu_img in menu_images:
            if self.bot.click_image(menu_img, timeout=5):
                logger.debug(f"Successfully found menu element: {menu_img}")
                return

        logger.warning("Could not find any menu elements")
    
    def _heal_pokemon(self) -> None:
        """Heal Pokemon if needed."""
        logger.debug("Checking if Pokemon need healing")
        
        # Look for Pokemon bag
        if self.bot.click_image("pkbss10.png", timeout=5):
            time.sleep(2)
            
            # Look for heal items
            if self.bot.detect_image("curars10.png"):
                self.bot.click_image("curars10.png")
                logger.debug("Healed Pokemon")
            
            # Go back
            self.bot.adb.back()
    
    def _navigate_to_galar_adventure(self) -> bool:
        """Navigate to Galar adventure.
        
        Returns:
            True if successfully navigated to adventure
        """
        logger.debug("Navigating to Galar adventure")
        
        # Look for adventure button
        if self.bot.click_image("adventure.png", timeout=10):
            time.sleep(2)
            
            # Look for Galar-specific elements
            if self.bot.wait_for_text("Galar", timeout=15):
                logger.debug("Found Galar adventure")
                return True
        
        logger.warning("Could not navigate to Galar adventure")
        return False
    
    def _execute_galar_adventure(self) -> bool:
        """Execute the main Galar adventure logic.
        
        Returns:
            True if adventure completed successfully
        """
        logger.debug("Executing Galar adventure")
        
        # Simplified adventure execution
        # In real implementation, this would contain the complex logic
        # from the original galar_adventure2 function
        
        try:
            # Look for adventure elements
            adventure_timeout = self.config.adventures.galar.get("timeout", 300)
            start_time = time.time()
            
            while time.time() - start_time < adventure_timeout:
                # Check for completion
                if self.bot.detect_image("adventure_complete.png"):
                    logger.info("Adventure completed successfully")
                    return True
                
                # Check for battle
                if self.bot.detect_image("battle.png"):
                    self._handle_battle()
                
                # Check for rewards
                if self.bot.detect_image("rewards.png"):
                    self._collect_rewards()
                
                # Continue adventure
                if self.bot.click_image("continue.png", timeout=2):
                    time.sleep(2)
                
                time.sleep(1)
            
            logger.warning("Adventure timed out")
            return False
            
        except Exception as e:
            logger.error(f"Error during adventure execution: {e}")
            return False
    
    def _find_dynamax_adventure(self) -> bool:
        """Find available Dynamax adventure.
        
        Returns:
            True if Dynamax adventure found
        """
        logger.debug("Searching for Dynamax adventure")
        
        # Look for Dynamax elements
        if self.bot.detect_image("dynamax.png"):
            logger.debug("Found Dynamax adventure")
            return True
        
        return False
    
    def _execute_dynamax_adventure(self) -> bool:
        """Execute Dynamax adventure.
        
        Returns:
            True if adventure completed successfully
        """
        logger.debug("Executing Dynamax adventure")
        
        # Simplified Dynamax logic
        try:
            adventure_timeout = self.config.adventures.dynamax.get("timeout", 600)
            start_time = time.time()
            
            while time.time() - start_time < adventure_timeout:
                # Check for completion
                if self.bot.detect_image("raid_complete.png"):
                    logger.info("Dynamax adventure completed")
                    return True
                
                # Handle battle phases
                if self.bot.detect_image("raid_battle.png"):
                    self._handle_raid_battle()
                
                time.sleep(2)
            
            return False
            
        except Exception as e:
            logger.error(f"Error during Dynamax adventure: {e}")
            return False
    
    def _handle_battle(self) -> None:
        """Handle battle interactions."""
        logger.debug("Handling battle")
        
        # Simple battle logic - click attack buttons
        if self.bot.click_image("attack.png", timeout=5):
            time.sleep(2)
    
    def _handle_raid_battle(self) -> None:
        """Handle raid battle interactions."""
        logger.debug("Handling raid battle")
        
        # Raid-specific battle logic
        if self.bot.click_image("raid_attack.png", timeout=5):
            time.sleep(2)
    
    def _collect_rewards(self) -> None:
        """Collect adventure rewards."""
        logger.debug("Collecting rewards")
        
        # Click on reward items
        if self.bot.click_image("collect.png", timeout=5):
            time.sleep(1)
    
    def _handle_adventure_completion(self, username: str) -> None:
        """Handle adventure completion tasks.
        
        Args:
            username: Username for logging
        """
        logger.info(f"Handling adventure completion for {username}")
        
        # Take screenshot of results
        screenshot_path = self.bot.take_screenshot()
        
        # Check for shiny Pokemon
        if self.bot.detect_image("shiny.png") or self.bot.detect_image("shiny70.png"):
            logger.info(f"SHINY POKEMON FOUND for {username}!")
            
            if self.bot.telegram:
                self.bot.telegram.send_shiny_found(username, screenshot_path=screenshot_path)
        
        # Send completion screenshot
        if self.bot.telegram:
            self.bot.telegram.send_photo(screenshot_path, f"Adventure completed for {username}")
    
    def get_available_adventures(self) -> Dict[str, bool]:
        """Get list of available adventures.
        
        Returns:
            Dictionary mapping adventure names to availability
        """
        return {
            "galar": self.config.adventures.galar.get("enabled", True),
            "dynamax": self.config.adventures.dynamax.get("enabled", True)
        }
