#!/usr/bin/env python3
"""Script simple para probar el bot sin complicaciones."""

import time
import logging
from pokemon_bot import PokemonBot

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_bot_simple():
    """Test simple del bot."""
    device_id = "************:5555"
    
    logger.info("🚀 Iniciando test simple del bot...")
    
    try:
        with PokemonBot(device_id) as bot:
            logger.info("✅ Bot conectado exitosamente")
            
            # Test 1: Reiniciar Pokemon GO
            logger.info("🎮 Test 1: Reiniciando Pokemon GO...")
            bot.restart_pokemon_go()
            time.sleep(10)
            
            # Test 2: Tomar screenshot
            logger.info("📸 Test 2: Tomando screenshot...")
            screenshot_path = bot.take_screenshot()
            logger.info(f"Screenshot guardado en: {screenshot_path}")
            
            # Test 3: Buscar imágenes comunes
            logger.info("🔍 Test 3: Buscando imágenes...")
            test_images = ["pkbss10.png", "adventure.png", "battle.png", "continue.png"]
            
            for img in test_images:
                coords = bot.detect_image(img)
                if coords:
                    logger.info(f"✅ {img} ENCONTRADA en {coords}")
                    # Hacer click en la primera imagen encontrada
                    if img == "pkbss10.png":
                        logger.info(f"👆 Haciendo click en {img}...")
                        bot.click(coords[0], coords[1])
                        time.sleep(3)
                        break
                else:
                    logger.info(f"❌ {img} no encontrada")
            
            # Test 4: Navegación básica
            logger.info("🧭 Test 4: Navegación básica...")
            for i in range(3):
                logger.info(f"Presionando botón back ({i+1}/3)...")
                bot.adb.back()
                time.sleep(1)
            
            # Test 5: Clicks aleatorios para simular actividad
            logger.info("🎯 Test 5: Simulando actividad...")
            click_positions = [
                (540, 1000),  # Centro-abajo
                (540, 1500),  # Más abajo
                (200, 800),   # Izquierda
                (880, 800),   # Derecha
            ]
            
            for i, (x, y) in enumerate(click_positions):
                logger.info(f"Click {i+1}/4 en ({x}, {y})")
                bot.click(x, y)
                time.sleep(2)
                
                # Tomar screenshot después de cada click
                screenshot_path = bot.take_screenshot()
                logger.info(f"Screenshot {i+1} guardado")
            
            logger.info("✅ Test completado exitosamente!")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error durante el test: {e}")
        return False

def test_continuous_simple():
    """Test continuo simple."""
    device_id = "************:5555"
    
    logger.info("🔄 Iniciando test continuo simple...")
    
    session_count = 0
    max_sessions = 3  # Solo 3 sesiones para test
    
    while session_count < max_sessions:
        session_count += 1
        logger.info(f"📋 Sesión {session_count}/{max_sessions}")
        
        try:
            success = test_bot_simple()
            
            if success:
                logger.info(f"✅ Sesión {session_count} completada exitosamente")
            else:
                logger.warning(f"⚠️ Sesión {session_count} falló")
            
            if session_count < max_sessions:
                logger.info("⏳ Esperando 10 segundos antes de la siguiente sesión...")
                time.sleep(10)
                
        except KeyboardInterrupt:
            logger.info("🛑 Test interrumpido por el usuario")
            break
        except Exception as e:
            logger.error(f"❌ Error en sesión {session_count}: {e}")
            time.sleep(5)
    
    logger.info(f"🏁 Test continuo completado. Total sesiones: {session_count}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--continuous":
        test_continuous_simple()
    else:
        test_bot_simple()
