# -*- coding: utf-8 -*-
"""
Created on Sun Dec 15 01:22:31 2024

@author: andre
"""
from pymongo import MongoClient
import os
import time

class MongoConfig:
   """Singleton for keeping the mongo config.
   I am avoiding making an enviroment variable.
   """
   __instance = None
   def __new__(cls):
     if cls.__instance is None:
            cls.__instance = super(MongoConfig, cls).__new__(cls)
            cls.__instance.__client = MongoClient(
             "mongodb+srv://pokebot:<EMAIL>/?retryWrites=true&w=majority")
            cls.__instance.__db=cls.__instance.__client.pokebot
     return cls.__instance

   def get_db(self):
      """returns a mongo client database object"""
      return self.__db
class MongoQuery:
    """
     Queries for User document with user parameter.
    """
    def __init__(self,user):
        self.user = user
        self.db = MongoConfig().get_db()
        self.collection = self.db.cuentas
    def query_user(self):
        query = {"user": self.user}
        if self.collection.find_one(query):
          return True
        return False

class MongoUpdater:
  """
   Update one user document by user param and sets  'status' to True.
   default user data is setted.
  """
  def __init__(self,user):
      self.user = user
      self.db = MongoConfig().get_db()
      self.collection=self.db.cuentas
  def update_document(self):
        query = {"user": self.user}
        update = {"$set": {"status": True,"time_updated":time.time(),"shiny":False,
                         "dynamax":False,  "galar":False }}
        self.collection.update_one(query,update)


class Mongo_Updater:
    """Update a user document setting its state to nameChanged"""
    def __init__(self, user):
         self.user = user
         self.db = MongoConfig().get_db()
         self.collection=self.db.cuentas
    def update_document(self):
         query = {"user": self.user}
         update = {"$set": {"nameChanged": True}}
         self.collection.update_one(query, update)
class MongoUpdaters:
    """Update user document and sets status ban"""
    def __init__(self, user):
         self.user = user
         self.db = MongoConfig().get_db()
         self.collection=self.db.cuentas
    def update_document(self):
         query = {"user": self.user}
         update = {"$set": {"status_ban": True}}
         self.collection.update_one(query, update)
class NewUser:
   """ Updates a user document by user and status to create and  sets create to true"""
   def __init__(self,user):
         self.user=user
         self.db = MongoConfig().get_db()
         self.collection=self.db.cuentas
   def update_document(self):
         query = {"user": self.user}
         update = {"$set": {"create": True, "status":False,
                            "time_updated":time.time(),"status_ban":False
                             }}
         self.collection.update_one(query, update)