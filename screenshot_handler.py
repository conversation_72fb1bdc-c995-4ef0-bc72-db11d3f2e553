# -*- coding: utf-8 -*-
"""
Created on Sun Dec 15 01:17:20 2024

@author: andre
"""

import cv2
import numpy as np
import tempfile
import os
from adb_handler import ADBHandler

class ScreenshotHandler:
    def __init__(self, device_id):
        self.adb_handler = ADB<PERSON>andler(device_id)
        self.device_id=device_id
    def capture(self):
       """Captures a screenshot and saves it to a temporary file."""
       try:
           image = self.adb_handler.screencap()
           screenshot_array = cv2.imdecode(np.frombuffer(image, dtype=np.uint8), cv2.IMREAD_COLOR)
           if screenshot_array is None:
             raise Exception("not able to decode the screen")
           temp_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False)
           cv2.imwrite(temp_file.name, screenshot_array)
           return temp_file.name
       except Exception as e:
          print(f"Error Capturing screenshot {e}")
          return None

    def get_cv2_image(self):
          image = self.adb_handler.screencap()
          screenshot_array = cv2.imdecode(np.frombuffer(image, dtype=np.uint8), cv2.IMREAD_COLOR)
          return screenshot_array