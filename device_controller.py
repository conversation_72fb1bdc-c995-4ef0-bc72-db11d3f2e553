#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import subprocess
import cv2
import numpy as np
import pytesseract
from ppadb.client import Client
import random
from vpn_manager import VPNManager



class MobileDeviceController:
    def __init__(self, device_id, host='localhost', port=5037):
        """
        Inicializa el controlador conectándose al dispositivo ADB indicado.

        :param device_id: Identificador del dispositivo.
        :param host: Dirección del servidor ADB (default: 'localhost').
        :param port: Puerto del servidor ADB (default: 5037).
        """
        self.client = Client(host=host, port=port)
        self.device = self.client.device(device_id)
        # Configurar la ruta al ejecutable de Tesseract
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe'
    
    def click(self, x, y):
        """
        Simula un toque en la posición (x, y) de la pantalla.

        :param x: Coordenada X.
        :param y: Coordenada Y.
        """
        self.device.shell(f'input tap {x} {y}')
        time.sleep(1)
    
    def restart_apps(self):
        """
        Reinicia las aplicaciones de forma completa.
        (Ejemplo: detiene Pokémon GO y otra app, inicia la segunda mediante monkey,
         luego realiza dos toques para finalizar el reinicio).
        """
        print("[INFO] Reiniciando aplicaciones (reinicio completo).")
        self.device.shell('am force-stop com.nianticlabs.pokemongo 1')
        self.device.shell('am force-stop com.evermorelabs.aerilate 1')
        self.device.shell('monkey -p com.evermorelabs.aerilate 1')
        time.sleep(10)
        self.click(570, 1291)
        self.click(570, 1077)
        time.sleep(2)
    
    def restart_pokemongo(self):
        """
        Reinicia únicamente la aplicación de Pokémon GO.
        """
        print("[INFO] Reiniciando Pokémon GO.")
        self.device.shell('am force-stop com.nianticlabs.pokemongo 1')
        self.device.shell('monkey -p com.nianticlabs.pokemongo 1')
        time.sleep(2)
    
    def get_screen_text(self):
        """
        Captura la pantalla del dispositivo y extrae el texto usando OCR (pytesseract).

        :return: Texto extraído de la imagen.
        """
        image = self.device.screencap()
        screenshot_array = cv2.imdecode(np.frombuffer(image, dtype=np.uint8), cv2.IMREAD_COLOR)
        text = pytesseract.image_to_string(screenshot_array)
        return text

    def set_proxy(self, proxy_address):
        """
        Configura el proxy en el dispositivo.

        :param proxy_address: Dirección del proxy (ejemplo: '************:3128').
        """
        self.device.shell(f'settings put global http_proxy {proxy_address}')
        print(f"[INFO] Proxy configurado a {proxy_address}")




    def restart_container_until_log(self, container_name, search_message, sleep_time=8, max_retries=50):
    
        attempt = 0
    
        while attempt < max_retries:
            print(f"Intento {attempt + 1}/{max_retries}: Reiniciando contenedor {container_name}...")
            
            # Reiniciar el contenedor
            subprocess.run(["docker", "restart", container_name], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            # Esperar unos segundos para que el contenedor arranque
            time.sleep(sleep_time)
    
            # Obtener los últimos 10 registros del contenedor
            logs = subprocess.run(["docker", "logs", "--tail", "10", container_name], capture_output=True, text=True)
    
            # Verificar si el mensaje aparece en los logs
            if search_message in logs.stdout:
                print("✅ Mensaje encontrado. Contenedor estable.")
                return True
            else:
                print("❌ Mensaje no encontrado, reintentando...")
    
            attempt += 1
    
        print("⚠️ Se alcanzó el número máximo de intentos sin éxito.")
        return False  # Si el mensaje no aparece después de los intentos máximos



    def quit_proxy(self):
        """
        Elimina la configuración del proxy.
        """
        self.device.shell('settings put global http_proxy :0')
    
    def refresh_error(self):
        """
        Simula la acción de refrescar la pantalla o la conexión,
        enviando varios comandos de 'keyevent' y realizando toques.
        """
        self.device.shell(f'su -c "rm -rf /data/data/com.android.chrome/app_chrome/Default/History*"')
            
            # Eliminar cookies
        self.device.shell(f' su -c "rm -rf /data/data/com.android.chrome/app_chrome/Default/Cookies*"')
            
            # Eliminar caché de Chrome
        self.device.shell(f'su -c "rm -rf /data/data/com.android.chrome/cache/*"')
        self.device.shell(f'su -c "rm -rf /data/data/com.android.chrome/app_cache/*"')
            
            # Eliminar datos de sesión (pestañas abiertas)
        self.device.shell(f' su -c "rm -rf /data/data/com.android.chrome/app_chrome/Default/Sessions/*"')
            
            # Reiniciar Chrome para aplicar cambios
        #self.device.shell(f' su -c "am force-stop com.android.chrome"')
        self.click(675, 120)
        self.click(675, 120)
        if "Resubmission" in self.get_screen_text():
            self.click(600, 925)
            time.sleep(5)
        if "reached" in self.get_screen_text():
            self.click(675, 120)
            self.click(675, 120)
        if "Error 15" not in self.get_screen_text():    
            self.restart_pokemongo()
        if "Powered by imperva" not in self.get_screen_text(): 
            self.restart_pokemongo()
        if  "internet" in self.get_screen_text():
            self.click(675, 120)
            self.click(675, 120)
        if  "seeing" in self.get_screen_text():
   
         #self.restart_pokemongo()
         self.device.shell('input keyevent 4')
         #self.device.shell('input keyevent 61')
         #self.device.shell('input keyevent 61')
         #self.device.shell('input keyevent 66')
        time.sleep(2)
    
    def _handle_proxy_error(self):
    # Eliminar historial de navegación

            self.refresh_error()
    
    def check_error(self):
        """
        Verifica si aparece el error "Error 15" en la pantalla.
        Antes de realizar la verificación, se maneja cualquier error de proxy.

        :return: True si se detecta "Error 15", False en caso contrario.
        """
        self._handle_proxy_error()
        
        text = self.get_screen_text()
        if "Error 15" in text:
            return True
        if "Powered by imperva" in text:
            return True
        
        if "reached" in text:
            return True


# Lista de contenedores a reiniciar (cada uno se maneja individualmente)





    def handle_errors(self):
        """
        Maneja el error de forma progresiva:
          1. Refresca sin realizar cambios.
          2. Cambia el proxy y refresca.
          3. Reinicia el contenedor Docker y refresca.
        Si después de estas acciones el error persiste, reinicia la aplicación Pokémon GO.
        """
        acciones = [
            lambda: print("[ACCION] Refrescando sin cambios adicionales."),
            lambda: self.set_proxy("192.168.1.19:3128"),
            lambda: (VPNManager("nordvpn-squid").run()),
            lambda: print("[ACCION] Refrescando sin cambios adicionales."),
            lambda: self.set_proxy("192.168.1.19:3129"),
            lambda: (VPNManager("nordvpn-squid2").run()),
            lambda: print("[ACCION] Refrescando sin cambios adicionales."),
            lambda: self.set_proxy("192.168.1.19:3130"),
            lambda: (VPNManager("nordvpn-squid3").run()),            
            lambda: print("[ACCION] Refrescando sin cambios adicionales."),
            lambda: self.set_proxy("192.168.1.19:3131"),
            lambda: (VPNManager("nordvpn-squid4").run()),            
            lambda: print("[ACCION] Refrescando sin cambios adicionales."),
            lambda: self.set_proxy("192.168.1.19:3132"),
            lambda: (VPNManager("nordvpn-squid5").run()),            
                                          
                
        ]
        self.quit_proxy()
        for _ in range(10):
            for accion in acciones:
                if self.check_error():
                    accion()
                    self.refresh_error()
                    if not self.check_error():
                        print("[INFO] Error resuelto, se detiene la secuencia.")
                        return
                else:
                    print("[INFO] No se detecta error, se continúa sin aplicar cambios.")
                    return
        # Si tras todos los intentos el error persiste, se reinicia Pokémon GO.
        self.restart_pokemongo()
    def handle_errors(self):
            """
            Maneja el error de forma progresiva con selección aleatoria pero manteniendo la relación proxy ↔ VPN:
              1. Refresca sin realizar cambios.
              2. Cambia a un proxy aleatorio y usa su VPN correspondiente.
              3. Reinicia el contenedor Docker vinculado al proxy.
            Si después de estas acciones el error persiste, reinicia la aplicación Pokémon GO.
            """
            proxies = [
                "************:3128",
                "************:3129",
                "************:3130",
                "************:3131",
                "************:3132"
            ]
        
            vpn_containers = [
                "nordvpn-squid",
                "nordvpn-squid2",
                "nordvpn-squid3",
                "nordvpn-squid4",
                "nordvpn-squid5"
            ]
        
            # Elegir aleatoriamente un índice para proxy y contenedor
            index = random.randint(0, len(proxies) - 1)
            proxy = proxies[index]
            vpn = vpn_containers[index]
        
            acciones = [
                lambda: print("[ACCION] Refrescando sin cambios adicionales."),
                lambda: self.set_proxy(proxy),  # Proxy y VPN están ligados
                lambda: VPNManager(vpn).run(),  # Se usa el VPN correspondiente
            ]
        
            self.quit_proxy()
        
            for _ in range(10):
                for accion in acciones:
                    if self.check_error():
                        accion()
                        self.refresh_error()
                        if not self.check_error():
                            print("[INFO] Error resuelto, se detiene la secuencia.")
                            return
                    else:
                        print("[INFO] No se detecta error, se continúa sin aplicar cambios.")
                        return
        
            # Si tras todos los intentos el error persiste, se reinicia Pokémon GO.
            print("[ERROR] No se pudo resolver el problema. Reiniciando Pokémon GO.")
            self.restart_pokemongo()

# Ejemplo de uso: este bloque se ejecuta cuando el módulo se ejecuta directamente.
if __name__ == '__main__':
    # Reemplaza 'R28M20P9AQL' con el id real de tu dispositivo.
    device_id = "R28M22DWS4A"
    controller = MobileDeviceController(device_id)
    controller.handle_errors()
