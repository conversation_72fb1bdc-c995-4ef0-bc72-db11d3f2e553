Metadata-Version: 2.4
Name: pokemon-bot
Version: 2.0.0
Summary: Advanced Pokemon GO automation bot with multi-device support
Home-page: https://github.com/pokemon-bot/pokemon-bot
Author: <PERSON><PERSON><PERSON> Team
Author-email: Pokemon Bot Team <<EMAIL>>
License: MIT
Keywords: pokemon,automation,bot,android,adb
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Dynamic: author
Dynamic: home-page
Dynamic: requires-python

# Pokemon Bot 2.0 - Advanced Pokemon GO Automation

A professional, enterprise-grade Pokemon GO automation bot with multi-device support, built following software engineering best practices.

## 🚀 Features

### Core Capabilities
- **Multi-device Support**: Manage multiple Android devices simultaneously
- **Advanced Image Detection**: Optimized template matching with caching
- **OCR Integration**: Text recognition with preprocessing and confidence scoring
- **Adventure Automation**: Galar adventures, Dynamax raids, and more
- **Real-time Monitoring**: Telegram notifications and logging
- **Database Integration**: MongoDB for user management and analytics

### Technical Excellence
- **SOLID Principles**: Clean, maintainable, and extensible code architecture
- **Error Handling**: Comprehensive retry logic and circuit breakers
- **Configuration Management**: YAML-based config with environment variable support
- **Professional Logging**: Structured logging with rotation and multiple outputs
- **Type Safety**: Full type hints and mypy compatibility
- **Testing**: Comprehensive unit and integration test suite

## 📋 Requirements

### System Requirements
- Python 3.8+
- Android Debug Bridge (ADB)
- Tesseract OCR
- MongoDB (optional)

### Android Device Requirements
- Android 5.0+ (API level 21+)
- USB Debugging enabled
- Pokemon GO installed

## 🛠️ Installation

### 1. Clone Repository
```bash
git clone https://github.com/your-org/pokemon-bot.git
cd pokemon-bot
```

### 2. Install Dependencies
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install package in development mode
pip install -e .

# Or install from requirements
pip install -r requirements.txt
```

### 3. Install System Dependencies

#### Windows
```bash
# Install Tesseract OCR
# Download from: https://github.com/UB-Mannheim/tesseract/wiki
# Add to PATH or update config with full path

# Install ADB (Android SDK Platform Tools)
# Download from: https://developer.android.com/studio/releases/platform-tools
```

#### Linux/macOS
```bash
# Ubuntu/Debian
sudo apt-get install tesseract-ocr android-tools-adb

# macOS
brew install tesseract android-platform-tools
```

### 4. Configuration Setup

#### Copy Configuration Templates
```bash
cp config/local.yaml.example config/local.yaml
cp .env.example .env
```

#### Edit Configuration Files
```yaml
# config/local.yaml
telegram:
  bot_token: "YOUR_BOT_TOKEN"
  chat_id: "YOUR_CHAT_ID"

mongodb:
  connection_string: "mongodb+srv://user:<EMAIL>/"

ocr:
  tesseract_cmd: "C:/Program Files (x86)/Tesseract-OCR/tesseract.exe"  # Windows
```

```bash
# .env
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here
MONGODB_CONNECTION_STRING=mongodb+srv://user:<EMAIL>/
```

## 🎮 Usage

### Command Line Interface

#### Basic Usage
```bash
# Single session with specific user
pokemon-bot --device 192.168.1.26:5555 --user myuser --password mypass --adventure galar

# Continuous mode with automatic user fetching
pokemon-bot --device 192.168.1.26:5555 --continuous --adventure auto

# Test device connection
pokemon-bot --device 192.168.1.26:5555 --test-device

# Take screenshot
pokemon-bot --device 192.168.1.26:5555 --screenshot screenshot.png
```

#### Advanced Options
```bash
# Debug mode with custom config
pokemon-bot --device emulator-5554 --debug --config /path/to/config

# Limited sessions with delay
pokemon-bot --device 192.168.1.26:5555 --continuous --max-sessions 10 --session-delay 30

# Specific adventure type
pokemon-bot --device 192.168.1.26:5555 --user myuser --password mypass --adventure dynamax
```

### Python API

```python
from pokemon_bot import PokemonBot

# Initialize bot
with PokemonBot("192.168.1.26:5555") as bot:
    # Start session
    if bot.start_session("username", "password"):
        # Run adventure
        success = bot.run_adventure("galar")
        
        # Take screenshot
        screenshot_path = bot.take_screenshot()
        
        # Send notification
        bot.send_notification("Adventure completed!", screenshot_path)
```

## 🏗️ Architecture

### Project Structure
```
pokemon-bot/
├── src/pokemon_bot/           # Main package
│   ├── core/                  # Core components
│   │   ├── bot.py            # Main bot class
│   │   ├── config.py         # Configuration management
│   │   ├── exceptions.py     # Custom exceptions
│   │   ├── logging.py        # Logging system
│   │   └── retry.py          # Retry mechanisms
│   ├── handlers/             # Device and service handlers
│   │   ├── adb_handler.py    # ADB communication
│   │   ├── image_handler.py  # Image detection
│   │   ├── ocr_handler.py    # Text recognition
│   │   └── screenshot_handler.py # Screenshot management
│   ├── services/             # Business logic services
│   │   ├── adventure_service.py # Adventure automation
│   │   ├── device_manager.py    # Device management
│   │   └── user_service.py      # User management
│   ├── strategies/           # Adventure strategies
│   └── utils/               # Utility functions
├── tests/                   # Test suite
├── config/                  # Configuration files
├── assets/images/          # Template images
└── docs/                   # Documentation
```

### Key Components

#### Core Classes
- **PokemonBot**: Main orchestrator class
- **ADBHandler**: Android device communication
- **ImageHandler**: Template matching and detection
- **OCRHandler**: Text recognition and processing
- **ConfigManager**: Configuration management

#### Services
- **AdventureService**: Adventure automation logic
- **DeviceManager**: Multi-device coordination
- **UserService**: User account management
- **TelegramHandler**: Notification system

## 🔧 Configuration

### Configuration Hierarchy
1. Default configuration (`config/default.yaml`)
2. Local overrides (`config/local.yaml`)
3. Environment variables (`.env`)
4. Command line arguments

### Key Configuration Sections

#### ADB Settings
```yaml
adb:
  host: "localhost"
  port: 5037
  connection_timeout: 30
  command_timeout: 10
```

#### Image Detection
```yaml
image_detection:
  threshold: 0.9
  resize_factor: 0.5
  cache_enabled: true
  cache_size: 100
```

#### Adventure Settings
```yaml
adventures:
  galar:
    enabled: true
    max_retries: 3
    timeout: 300
  dynamax:
    enabled: true
    max_retries: 3
    timeout: 600
```

## 🧪 Testing

### Run Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=pokemon_bot --cov-report=html

# Run specific test category
pytest -m unit
pytest -m integration
```

### Test Categories
- **Unit Tests**: Individual component testing
- **Integration Tests**: End-to-end workflow testing
- **Performance Tests**: Load and stress testing

## 📊 Monitoring & Logging

### Logging Features
- **Structured Logging**: JSON format for easy parsing
- **Multiple Outputs**: Console and file logging
- **Log Rotation**: Automatic log file management
- **Context Enrichment**: Device and user context in logs

### Monitoring
- **Health Checks**: Device connectivity monitoring
- **Performance Metrics**: Adventure success rates and timing
- **Error Tracking**: Comprehensive error reporting
- **Telegram Notifications**: Real-time status updates

## 🔒 Security

### Best Practices
- **Environment Variables**: Sensitive data stored securely
- **Input Validation**: All inputs validated and sanitized
- **Error Handling**: No sensitive data in error messages
- **Access Control**: Database and API access controls

## 🤝 Contributing

### Development Setup
```bash
# Install development dependencies
pip install -e ".[dev]"

# Install pre-commit hooks
pre-commit install

# Run code formatting
black src/ tests/
isort src/ tests/

# Run type checking
mypy src/
```

### Code Standards
- **PEP 8**: Python style guide compliance
- **Type Hints**: Full type annotation coverage
- **Documentation**: Comprehensive docstrings
- **Testing**: Minimum 80% code coverage

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This software is for educational purposes only. Use at your own risk and ensure compliance with Pokemon GO's Terms of Service and local laws.

## 🆘 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/your-org/pokemon-bot/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/pokemon-bot/discussions)

---

**Pokemon Bot 2.0** - Professional automation with enterprise-grade reliability.
