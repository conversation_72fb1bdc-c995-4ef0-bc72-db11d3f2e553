#!/usr/bin/env python3
"""Script para probar la detección de imágenes con el nuevo threshold."""

from pokemon_bot import PokemonBot
import time

def test_image_detection():
    print("🎯 Probando detección de imágenes con threshold 0.6")
    print("=" * 50)
    
    device_id = "************:5555"
    
    try:
        with PokemonBot(device_id) as bot:
            print(f"✅ Conectado al dispositivo: {device_id}")
            
            # Lista de imágenes para probar
            test_images = [
                "adventure.png",
                "pkbss10.png", 
                "battle.png",
                "continue.png",
                "rewards.png",
                "menu.png",
                "100shiny.png"  # Esta tuvo 0.697 de confianza
            ]
            
            print(f"\n🔍 Probando detección de {len(test_images)} imágenes...")
            
            for img_name in test_images:
                print(f"\n   Buscando: {img_name}")
                
                coords = bot.detect_image(img_name)
                
                if coords:
                    print(f"   ✅ ENCONTRADA en coordenadas: {coords}")
                    
                    # Opcional: hacer click para probar
                    # bot.click(coords[0], coords[1])
                    # time.sleep(1)
                else:
                    print(f"   ❌ No encontrada")
            
            # Probar detección múltiple
            print(f"\n🎯 Probando detección múltiple...")
            results = bot.image_handler.detect_multiple(
                bot.adb.get_cv2_image(),
                test_images[:3]  # Solo las primeras 3
            )
            
            for img_name, coords in results.items():
                status = "✅ ENCONTRADA" if coords else "❌ No encontrada"
                print(f"   {img_name}: {status}")
                if coords:
                    print(f"      Coordenadas: {coords}")
            
            print(f"\n✅ Prueba de detección completada!")
            
    except Exception as e:
        print(f"❌ Error durante la prueba: {e}")

if __name__ == "__main__":
    test_image_detection()
