class ShellInputEscaper:
    def __init__(self):
        self.special_chars = {
            '$': '\\$',
            '"': '\\"',
            '\'': '\\\'',
            '`': '\\`'
        }

    def escape_special_chars(self, input_text):
        escaped_text = ''
        for char in input_text:
            if char in self.special_chars:
                escaped_text += self.special_chars[char]
            else:
                escaped_text += char
        return escaped_text

    def generate_shell_command(self, input_text):
        escaped_text = self.escape_special_chars(input_text)
        return escaped_text
