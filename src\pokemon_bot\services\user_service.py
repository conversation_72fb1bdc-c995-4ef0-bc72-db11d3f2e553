"""User management services."""

import requests
import json
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
from datetime import datetime

from ..core.config import get_config
from ..core.exceptions import PokemonBotError
from ..core.logging import get_logger
from ..handlers.database_handler import DatabaseHandler

logger = get_logger(__name__)


class UserService:
    """Service for managing user accounts and status."""
    
    def __init__(self, database_handler: Optional[DatabaseHandler] = None):
        """Initialize user service.
        
        Args:
            database_handler: Optional database handler instance
        """
        self.config = get_config()
        self.database = database_handler
    
    def get_user(self, username: str) -> Optional[Dict[str, Any]]:
        """Get user information.
        
        Args:
            username: Username to lookup
            
        Returns:
            User information dictionary or None if not found
        """
        if not self.database:
            logger.warning("Database not available for user lookup")
            return None
        
        try:
            return self.database.find_user(username)
        except Exception as e:
            logger.error(f"Failed to get user {username}: {e}")
            return None
    
    def update_user_status(self, username: str, status: str, **kwargs) -> bool:
        """Update user status.
        
        Args:
            username: Username to update
            status: New status
            **kwargs: Additional fields to update
            
        Returns:
            True if update successful
        """
        if not self.database:
            logger.warning("Database not available for user status update")
            return False
        
        try:
            return self.database.update_user_status(username, status, **kwargs)
        except Exception as e:
            logger.error(f"Failed to update user status for {username}: {e}")
            return False
    
    def mark_user_completed(self, username: str, adventure_type: str, duration: float) -> None:
        """Mark user adventure as completed.
        
        Args:
            username: Username
            adventure_type: Type of adventure completed
            duration: Adventure duration in seconds
        """
        try:
            # Update user status
            self.update_user_status(username, "completed", last_adventure=adventure_type)
            
            # Log adventure
            if self.database:
                self.database.log_adventure(username, adventure_type, True, duration)
            
            logger.info(f"Marked user {username} as completed for {adventure_type}")
            
        except Exception as e:
            logger.error(f"Failed to mark user completed: {e}")
    
    def mark_user_failed(self, username: str, adventure_type: str, error: str) -> None:
        """Mark user adventure as failed.
        
        Args:
            username: Username
            adventure_type: Type of adventure that failed
            error: Error message
        """
        try:
            # Update user status
            self.update_user_status(username, "failed", last_error=error)
            
            # Log adventure failure
            if self.database:
                self.database.log_adventure(username, adventure_type, False, 0, error=error)
            
            logger.info(f"Marked user {username} as failed for {adventure_type}: {error}")
            
        except Exception as e:
            logger.error(f"Failed to mark user failed: {e}")
    
    def get_user_stats(self, username: str) -> Dict[str, Any]:
        """Get user statistics.
        
        Args:
            username: Username
            
        Returns:
            User statistics dictionary
        """
        if not self.database:
            return {"error": "Database not available"}
        
        try:
            return self.database.get_user_stats(username)
        except Exception as e:
            logger.error(f"Failed to get user stats for {username}: {e}")
            return {"error": str(e)}
    
    def is_user_banned(self, username: str) -> bool:
        """Check if user is banned.
        
        Args:
            username: Username to check
            
        Returns:
            True if user is banned
        """
        user = self.get_user(username)
        if not user:
            return False
        
        return user.get("status_ban", False)
    
    def ban_user(self, username: str, reason: str = "Violation of terms") -> bool:
        """Ban a user.
        
        Args:
            username: Username to ban
            reason: Reason for ban
            
        Returns:
            True if ban successful
        """
        try:
            success = self.update_user_status(
                username, 
                "banned", 
                status_ban=True, 
                ban_reason=reason,
                ban_time=datetime.now().isoformat()
            )
            
            if success:
                logger.warning(f"User {username} has been banned: {reason}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to ban user {username}: {e}")
            return False


class UserDataService:
    """Service for fetching user credentials from external API."""
    
    def __init__(self, api_base_url: Optional[str] = None):
        """Initialize user data service.
        
        Args:
            api_base_url: Base URL for user data API
        """
        self.config = get_config()
        self.api_base_url = api_base_url or "http://localhost:5001"
        self.timeout = 30
    
    def fetch_next_user(self) -> Optional[Tuple[str, str]]:
        """Fetch next user credentials from API.
        
        Returns:
            Tuple of (username, password) or None if no user available
        """
        try:
            url = f"{self.api_base_url}/acc2"
            response = requests.get(url, timeout=self.timeout)
            
            if response.status_code != 200:
                logger.warning(f"API returned status {response.status_code}")
                return None
            
            # Parse response
            data_str = response.text.strip('"')
            
            # Clean up the JSON string
            data_str = data_str.replace("ObjectId('", '"').replace("')", '"')
            data_str = data_str.replace("False", '"false"')
            data_str = data_str.replace("'", '"').replace('"\n', '')
            
            # Parse JSON
            data = json.loads(data_str)
            
            username = data.get('acc')
            password = data.get('pwd')
            
            if username and password:
                logger.info(f"Fetched user credentials for: {username}")
                return (username, password)
            else:
                logger.warning("Invalid user data received from API")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch user data from API: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse user data JSON: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching user data: {e}")
            return None
    
    def mark_user_completed(self, username: str, password: str) -> None:
        """Mark user as completed (remove from queue).

        Args:
            username: Username
            password: Password
        """
        try:
            # Call the API endpoint to mark user as completed
            url = f"{self.api_base_url}/res2"
            response = requests.get(url, timeout=self.timeout)

            if response.status_code == 200:
                logger.info(f"User {username} marked as completed successfully")
            else:
                logger.warning(f"Failed to mark user completed - API returned {response.status_code}")

        except Exception as e:
            logger.error(f"Failed to mark user completed: {e}")
    
    def mark_user_failed(self, username: str, password: str, error: str = "") -> None:
        """Mark user as failed.

        Args:
            username: Username
            password: Password
            error: Error message
        """
        try:
            # Log the failure
            logger.warning(f"User {username} marked as failed: {error}")

            # Call the API endpoint to mark user as failed (same as completed for now)
            url = f"{self.api_base_url}/res2"
            response = requests.get(url, timeout=self.timeout)

            if response.status_code == 200:
                logger.info(f"User {username} marked as failed successfully")
            else:
                logger.warning(f"Failed to mark user failed - API returned {response.status_code}")

        except Exception as e:
            logger.error(f"Failed to mark user failed: {e}")
    
    def reset_user_queue(self) -> bool:
        """Reset the user queue (if supported by API).
        
        Returns:
            True if reset successful
        """
        try:
            url = f"{self.api_base_url}/res2"
            response = requests.get(url, timeout=self.timeout)
            
            if response.status_code == 200:
                logger.info("User queue reset successfully")
                return True
            else:
                logger.warning(f"Failed to reset user queue: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to reset user queue: {e}")
            return False
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get status of user queue.
        
        Returns:
            Dictionary with queue status information
        """
        try:
            # This would call an API endpoint to get queue status
            # For now, return basic info
            return {
                "api_url": self.api_base_url,
                "status": "unknown",
                "message": "Queue status endpoint not implemented"
            }
            
        except Exception as e:
            logger.error(f"Failed to get queue status: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
