"""Advanced logging system for Pokemon Bot."""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import json
from datetime import datetime

from .config import get_config
from .exceptions import ConfigurationError


class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields
        if hasattr(record, 'device_id'):
            log_entry["device_id"] = record.device_id
        if hasattr(record, 'user'):
            log_entry["user"] = record.user
        if hasattr(record, 'adventure_type'):
            log_entry["adventure_type"] = record.adventure_type
        if hasattr(record, 'duration'):
            log_entry["duration"] = record.duration
        
        return json.dumps(log_entry, ensure_ascii=False)


class ColoredFormatter(logging.Formatter):
    """Colored formatter for console output."""
    
    COLORS = {
        'DEBUG': '\033[36m',      # Cyan
        'INFO': '\033[32m',       # Green
        'WARNING': '\033[33m',    # Yellow
        'ERROR': '\033[31m',      # Red
        'CRITICAL': '\033[35m',   # Magenta
    }
    RESET = '\033[0m'
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record with colors."""
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.RESET}"
        
        return super().format(record)


class DeviceContextFilter(logging.Filter):
    """Filter to add device context to log records."""
    
    def __init__(self, device_id: Optional[str] = None):
        super().__init__()
        self.device_id = device_id
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Add device context to log record."""
        if self.device_id and not hasattr(record, 'device_id'):
            record.device_id = self.device_id
        return True


class PokemonBotLogger:
    """Main logger class for Pokemon Bot."""
    
    def __init__(self, name: str = "pokemon_bot"):
        self.name = name
        self.logger = logging.getLogger(name)
        self.config = get_config()
        self._setup_logging()
    
    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Set logging level
        level = getattr(logging, self.config.logging.level.upper(), logging.INFO)
        self.logger.setLevel(level)
        
        # Setup file logging
        if self.config.logging.file.get("enabled", True):
            self._setup_file_logging()
        
        # Setup console logging
        if self.config.logging.console.get("enabled", True):
            self._setup_console_logging()
        
        # Prevent propagation to root logger
        self.logger.propagate = False
    
    def _setup_file_logging(self) -> None:
        """Setup file logging with rotation."""
        log_path = Path(self.config.logging.file.get("path", "logs/pokemon_bot.log"))
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        max_size = self.config.logging.file.get("max_size_mb", 10) * 1024 * 1024
        backup_count = self.config.logging.file.get("backup_count", 5)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_path,
            maxBytes=max_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        
        # Use JSON formatter for file logs
        file_formatter = JSONFormatter()
        file_handler.setFormatter(file_formatter)
        
        self.logger.addHandler(file_handler)
    
    def _setup_console_logging(self) -> None:
        """Setup console logging."""
        console_handler = logging.StreamHandler(sys.stdout)
        
        # Use colored formatter if enabled
        if self.config.logging.console.get("colored", True):
            console_formatter = ColoredFormatter(self.config.logging.format)
        else:
            console_formatter = logging.Formatter(self.config.logging.format)
        
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
    
    def add_device_context(self, device_id: str) -> None:
        """Add device context to all log messages."""
        device_filter = DeviceContextFilter(device_id)
        for handler in self.logger.handlers:
            handler.addFilter(device_filter)
    
    def debug(self, message: str, **kwargs) -> None:
        """Log debug message."""
        self.logger.debug(message, extra=kwargs)
    
    def info(self, message: str, **kwargs) -> None:
        """Log info message."""
        self.logger.info(message, extra=kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """Log warning message."""
        self.logger.warning(message, extra=kwargs)
    
    def error(self, message: str, exc_info: bool = False, **kwargs) -> None:
        """Log error message."""
        self.logger.error(message, exc_info=exc_info, extra=kwargs)
    
    def critical(self, message: str, exc_info: bool = False, **kwargs) -> None:
        """Log critical message."""
        self.logger.critical(message, exc_info=exc_info, extra=kwargs)
    
    def log_adventure_start(self, adventure_type: str, user: str, device_id: str) -> None:
        """Log adventure start."""
        self.info(
            f"Starting {adventure_type} adventure for user {user}",
            adventure_type=adventure_type,
            user=user,
            device_id=device_id
        )
    
    def log_adventure_complete(self, adventure_type: str, user: str, device_id: str, duration: float) -> None:
        """Log adventure completion."""
        self.info(
            f"Completed {adventure_type} adventure for user {user} in {duration:.2f}s",
            adventure_type=adventure_type,
            user=user,
            device_id=device_id,
            duration=duration
        )
    
    def log_adventure_error(self, adventure_type: str, user: str, device_id: str, error: Exception) -> None:
        """Log adventure error."""
        self.error(
            f"Error in {adventure_type} adventure for user {user}: {error}",
            exc_info=True,
            adventure_type=adventure_type,
            user=user,
            device_id=device_id
        )
    
    def log_device_action(self, action: str, device_id: str, **kwargs) -> None:
        """Log device action."""
        self.debug(
            f"Device action: {action}",
            device_id=device_id,
            action=action,
            **kwargs
        )
    
    def log_image_detection(self, image_name: str, found: bool, device_id: str, **kwargs) -> None:
        """Log image detection result."""
        status = "found" if found else "not found"
        self.debug(
            f"Image detection: {image_name} {status}",
            device_id=device_id,
            image_name=image_name,
            found=found,
            **kwargs
        )
    
    def log_ocr_result(self, text: str, device_id: str, region: Optional[tuple] = None) -> None:
        """Log OCR result."""
        self.debug(
            f"OCR result: '{text[:50]}{'...' if len(text) > 50 else ''}'",
            device_id=device_id,
            text_length=len(text),
            region=region
        )


# Global logger instance
_logger: Optional[PokemonBotLogger] = None


def get_logger(name: str = "pokemon_bot") -> PokemonBotLogger:
    """Get the global logger instance."""
    global _logger
    if _logger is None:
        _logger = PokemonBotLogger(name)
    return _logger


def setup_logging(config_override: Optional[Dict[str, Any]] = None) -> PokemonBotLogger:
    """Setup logging with optional configuration override."""
    global _logger
    _logger = PokemonBotLogger()
    return _logger
