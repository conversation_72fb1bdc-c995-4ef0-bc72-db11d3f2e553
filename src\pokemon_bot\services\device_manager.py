"""Device management service."""

from typing import Dict, Any, Optional
from ..core.logging import get_logger
from ..handlers.adb_handler import ADBHandler

logger = get_logger(__name__)


class DeviceManager:
    """Service for managing device operations."""
    
    def __init__(self, adb_handler: ADBHandler):
        """Initialize device manager.
        
        Args:
            adb_handler: ADB handler instance
        """
        self.adb = adb_handler
        self.device_id = adb_handler.device_id
    
    def get_device_status(self) -> Dict[str, Any]:
        """Get comprehensive device status.
        
        Returns:
            Dictionary with device status information
        """
        try:
            device_info = self.adb.get_device_info()
            
            status = {
                "device_id": self.device_id,
                "connected": self.adb.is_connected(),
                "info": device_info
            }
            
            return status
            
        except Exception as e:
            logger.error(f"Failed to get device status: {e}")
            return {
                "device_id": self.device_id,
                "connected": False,
                "error": str(e)
            }
    
    def setup_device(self) -> bool:
        """Setup device for bot operations.
        
        Returns:
            True if setup successful
        """
        try:
            # Disable screen rotation
            self.adb.shell("content insert --uri content://settings/system --bind name:s:accelerometer_rotation --bind value:i:0")
            self.adb.shell("content insert --uri content://settings/system --bind name:s:user_rotation --bind value:i:0")
            
            logger.info("Device setup completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup device: {e}")
            return False
    
    def health_check(self) -> bool:
        """Perform device health check.
        
        Returns:
            True if device is healthy
        """
        try:
            # Test basic connectivity
            if not self.adb.is_connected():
                return False
            
            # Test shell command
            result = self.adb.shell("echo 'health_check'")
            if "health_check" not in result:
                return False
            
            # Test screenshot capability
            screenshot_data = self.adb.screencap()
            if not screenshot_data:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Device health check failed: {e}")
            return False
