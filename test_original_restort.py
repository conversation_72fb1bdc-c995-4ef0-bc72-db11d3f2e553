#!/usr/bin/env python3
"""Test script for original _restort functionality."""

import time
import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from pokemon_bot.handlers.adb_handler import ADBHandler

class OriginalRestort:
    """Original _restort functionality."""
    
    def __init__(self, adb_handler):
        self.adb_handler = adb_handler
    
    def _restort(self, user, pas):
        """Restores player preferences from a backup."""
        print(f"🔄 Starting _restort for user: {user}")
        
        # Eliminar historial de Chrome
        print("🧹 Cleaning Chrome history...")
        self.adb_handler.shell(f'su -c "rm -rf /data/data/com.android.chrome/app_chrome/Default/History*"')
            
        # Eliminar cookies
        print("🧹 Cleaning Chrome cookies...")
        self.adb_handler.shell(f' su -c "rm -rf /data/data/com.android.chrome/app_chrome/Default/Cookies*"')
            
        # Eliminar caché de Chrome
        print("🧹 Cleaning Chrome cache...")
        self.adb_handler.shell(f'su -c "rm -rf /data/data/com.android.chrome/cache/*"')
        self.adb_handler.shell(f'su -c "rm -rf /data/data/com.android.chrome/app_cache/*"')
            
        # Eliminar datos de sesión (pestañas abiertas)
        print("🧹 Cleaning Chrome sessions...")
        self.adb_handler.shell(f' su -c "rm -rf /data/data/com.android.chrome/app_chrome/Default/Sessions/*"')
            
        # Reiniciar Chrome para aplicar cambios
        print("🔄 Force stopping Chrome...")
        self.adb_handler.shell(f' su -c "am force-stop com.android.chrome"')
        
        print("🔄 Force stopping Pokemon GO...")
        self.adb_handler.shell('am force-stop com.nianticlabs.pokemongo')
        time.sleep(1)
        self.adb_handler.shell('am force-stop com.nianticlabs.pokemongo')
        time.sleep(1)
        
        # Check if user backup exists
        dest = os.path.join("c:/temp/", user)
        print(f"📁 Checking backup file: {dest}")
        
        if not os.path.exists(dest):
            print("❌ User backup file doesn't exist - using default")
            dest = os.path.join("c:/temp/retrt")
            
            if not os.path.exists(dest):
                print(f"❌ Default backup file also doesn't exist: {dest}")
                return False
            
            print("🔄 Force stopping Pokemon GO again...")
            self.adb_handler.shell('am force-stop com.nianticlabs.pokemongo')
            
            print("🗑️ Removing current playerprefs...")
            self.adb_handler.shell("su --mount-master -c 'rm -rf /data/data/com.nianticlabs.pokemongo/shared_prefs/com.nianticlabs.pokemongo.v2.playerprefs.xml'")
            
            print("📁 Creating directory...")
            self.adb_handler.shell(f"mkdir /sdcard/acc/ -p")
            
            print("📤 Pushing default backup...")
            self.adb_handler.push(dest, f'/sdcard/acc/retrt')
            
            print("📋 Copying to playerprefs...")
            self.adb_handler.shell(f"su --mount-master -c 'cp /sdcard/acc/retrt /data/data/com.nianticlabs.pokemongo/shared_prefs/com.nianticlabs.pokemongo.v2.playerprefs.xml'")
           
            print("🔄 Calling _reini...")
            self._reini(user)
        else:    
            print("✅ User backup found - restoring playerprefs")
            
            print("📁 Creating directory...")
            self.adb_handler.shell(f"mkdir /sdcard/acc/ -p")
            
            print(f"📤 Pushing user backup: {user}")
            self.adb_handler.push(dest, f'/sdcard/acc/{user}')
            
            print("📋 Copying to playerprefs...")
            self.adb_handler.shell(f"su --mount-master -c 'cp /sdcard/acc/{user} /data/data/com.nianticlabs.pokemongo/shared_prefs/com.nianticlabs.pokemongo.v2.playerprefs.xml'")
            
            print("🔄 Calling _reini...")
            self._reini(user)
        
        print("✅ _restort completed!")
        print("######")
        return True
    
    def _reini(self, user):
        """Restart Pokemon GO (mock implementation)."""
        print(f"🔄 _reini called for user: {user}")
        print("🔄 Force stopping Pokemon GO...")
        self.adb_handler.shell('am force-stop com.nianticlabs.pokemongo')
        time.sleep(2)
        
        print("🚀 Starting Pokemon GO...")
        self.adb_handler.shell('am start -n com.nianticlabs.pokemongo/com.nianticproject.holoholo.libholoholo.unity.UnityPlayerActivity')
        time.sleep(3)
        
        print(f"✅ _reini completed for user: {user}")

def test_original_restort():
    """Test the original _restort functionality."""
    print("🚀 Testing Original _restort Function")
    print("=" * 50)
    
    # Initialize ADB
    device_id = "192.168.1.26:5555"
    adb = ADBHandler(device_id)
    
    if not adb.is_connected():
        print("❌ Failed to connect to ADB device")
        return False
    
    print(f"✅ Connected to device: {device_id}")
    
    # Create original restort instance
    restort = OriginalRestort(adb)
    
    # Test with a specific user
    test_user = "AndresAf1002"
    test_password = "test123"
    
    print(f"\n🔄 Testing _restort for user: {test_user}")
    
    try:
        # Test the original _restort function
        result = restort._restort(test_user, test_password)
        
        if result:
            print("\n✅ Original _restort completed successfully!")
        else:
            print("\n❌ Original _restort failed!")
        
        return result
        
    except Exception as e:
        print(f"\n❌ Error during _restort: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_backup_files():
    """Check if backup files exist."""
    print("\n📁 Checking backup files...")
    
    users_to_check = ["AndresAf1001", "AndresAf1002", "retrt"]
    
    for user in users_to_check:
        backup_path = os.path.join("c:/temp/", user)
        exists = os.path.exists(backup_path)
        size = os.path.getsize(backup_path) if exists else 0
        print(f"  {user}: {'✅ EXISTS' if exists else '❌ NOT FOUND'} - {backup_path} ({size} bytes)")
    
    return True

if __name__ == "__main__":
    print("🚀 Original _restort Test")
    print("=" * 50)
    
    # Check backup files first
    check_backup_files()
    
    # Test original _restort
    success = test_original_restort()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Original _restort test passed!")
    else:
        print("💥 Original _restort test failed!")
