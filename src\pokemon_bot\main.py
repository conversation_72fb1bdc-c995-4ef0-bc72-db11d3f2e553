"""Main entry point for Pokemon Bot."""

import sys
import argparse
import signal
import time
from typing import Optional
from pathlib import Path

from .core.bot import PokemonBot
from .core.config import get_config, reload_config
from .core.logging import get_logger, setup_logging
from .core.exceptions import PokemonBotError
from .services.user_service import UserDataService

logger = get_logger(__name__)


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    logger.info(f"Received signal {signum}, shutting down gracefully...")
    sys.exit(0)


def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown."""
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Pokemon Bot - Advanced Pokemon GO automation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --device ************:5555 --adventure galar
  %(prog)s --device emulator-5554 --user myuser --password mypass
  %(prog)s --config /path/to/config --debug
        """
    )
    
    # Device configuration
    parser.add_argument(
        "--device", "-d",
        required=True,
        help="Android device ID (IP:port or serial)"
    )
    
    # User credentials
    parser.add_argument(
        "--user", "-u",
        help="Username for login"
    )
    
    parser.add_argument(
        "--password", "-p",
        help="Password for login"
    )
    
    # Adventure type
    parser.add_argument(
        "--adventure", "-a",
        choices=["galar", "dynamax", "auto"],
        default="auto",
        help="Type of adventure to run"
    )
    
    # Configuration
    parser.add_argument(
        "--config", "-c",
        help="Path to configuration directory"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode"
    )
    
    # Operational modes
    parser.add_argument(
        "--continuous",
        action="store_true",
        help="Run continuously with automatic user fetching"
    )
    
    parser.add_argument(
        "--max-sessions",
        type=int,
        default=0,
        help="Maximum number of sessions to run (0 for unlimited)"
    )
    
    parser.add_argument(
        "--session-delay",
        type=int,
        default=5,
        help="Delay between sessions in seconds"
    )
    
    # Utility commands
    parser.add_argument(
        "--test-device",
        action="store_true",
        help="Test device connection and exit"
    )
    
    parser.add_argument(
        "--screenshot",
        help="Take screenshot and save to specified path"
    )

    parser.add_argument(
        "--restart-game",
        action="store_true",
        help="Restart Pokemon GO and exit"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="Pokemon Bot 2.0.0"
    )
    
    return parser.parse_args()


def test_device_connection(device_id: str) -> bool:
    """Test device connection.
    
    Args:
        device_id: Device identifier
        
    Returns:
        True if connection successful
    """
    try:
        logger.info(f"Testing connection to device {device_id}")
        
        with PokemonBot(device_id) as bot:
            device_info = bot.adb.get_device_info()
            
            logger.info("Device connection successful!")
            logger.info(f"Device info: {device_info}")
            
            return True
            
    except Exception as e:
        logger.error(f"Device connection failed: {e}")
        return False


def take_screenshot(device_id: str, save_path: str) -> bool:
    """Take a screenshot.

    Args:
        device_id: Device identifier
        save_path: Path to save screenshot

    Returns:
        True if screenshot taken successfully
    """
    try:
        logger.info(f"Taking screenshot from device {device_id}")

        with PokemonBot(device_id) as bot:
            screenshot_path = bot.screenshot_handler.capture(save_path)
            logger.info(f"Screenshot saved to: {screenshot_path}")
            return True

    except Exception as e:
        logger.error(f"Failed to take screenshot: {e}")
        return False


def restart_pokemon_go(device_id: str) -> bool:
    """Restart Pokemon GO.

    Args:
        device_id: Device identifier

    Returns:
        True if restart successful
    """
    try:
        logger.info(f"Restarting Pokemon GO on device {device_id}")

        with PokemonBot(device_id) as bot:
            success = bot.restart_pokemon_go()
            if success:
                logger.info("Pokemon GO restarted successfully")
            else:
                logger.warning("Pokemon GO restart may have failed")
            return success

    except Exception as e:
        logger.error(f"Failed to restart Pokemon GO: {e}")
        return False


def run_single_session(device_id: str, user: str, password: str, adventure_type: str) -> bool:
    """Run a single bot session.
    
    Args:
        device_id: Device identifier
        user: Username
        password: Password
        adventure_type: Type of adventure to run
        
    Returns:
        True if session completed successfully
    """
    try:
        logger.info(f"Starting single session for user {user}")
        
        with PokemonBot(device_id) as bot:
            # Start session
            if not bot.start_session(user, password):
                logger.error("Failed to start session")
                return False
            
            # Run adventure
            if adventure_type != "auto":
                success = bot.run_adventure(adventure_type)
                if not success:
                    logger.error(f"Adventure {adventure_type} failed")
                    return False
            else:
                # Auto mode - run available adventures
                logger.info("Running in auto mode")
                # TODO: Implement auto adventure selection logic
            
            logger.info("Session completed successfully")
            return True
            
    except Exception as e:
        logger.error(f"Session failed: {e}", exc_info=True)
        return False


def run_continuous_mode(device_id: str, adventure_type: str, max_sessions: int, session_delay: int) -> None:
    """Run bot in continuous mode.
    
    Args:
        device_id: Device identifier
        adventure_type: Type of adventure to run
        max_sessions: Maximum number of sessions (0 for unlimited)
        session_delay: Delay between sessions
    """
    logger.info("Starting continuous mode")
    
    user_service = UserDataService()
    session_count = 0
    
    try:
        while max_sessions == 0 or session_count < max_sessions:
            try:
                # Fetch next user
                user_data = user_service.fetch_next_user()
                if not user_data:
                    logger.warning("No user data available, waiting...")
                    time.sleep(30)
                    continue
                
                user, password = user_data
                logger.info(f"Processing user {user} (session {session_count + 1})")
                
                # Run session
                success = run_single_session(device_id, user, password, adventure_type)
                
                if success:
                    logger.info(f"Session {session_count + 1} completed successfully")
                    user_service.mark_user_completed(user, password)
                else:
                    logger.error(f"Session {session_count + 1} failed")
                    user_service.mark_user_failed(user, password)
                
                session_count += 1
                
                # Delay between sessions
                if session_delay > 0:
                    logger.info(f"Waiting {session_delay} seconds before next session")
                    time.sleep(session_delay)
                
            except KeyboardInterrupt:
                logger.info("Received interrupt signal, stopping...")
                break
            except Exception as e:
                logger.error(f"Error in continuous mode: {e}", exc_info=True)
                time.sleep(10)  # Wait before retrying
    
    except Exception as e:
        logger.error(f"Fatal error in continuous mode: {e}", exc_info=True)
    
    logger.info(f"Continuous mode ended after {session_count} sessions")


def main():
    """Main entry point."""
    # Parse arguments
    args = parse_arguments()
    
    # Setup signal handlers
    setup_signal_handlers()
    
    try:
        # Load configuration
        if args.config:
            config = reload_config(args.config)
        else:
            config = get_config()
        
        # Setup logging
        if args.debug:
            config.logging.level = "DEBUG"
        
        setup_logging()
        
        logger.info("Pokemon Bot starting...")
        logger.info(f"Device: {args.device}")
        
        # Handle utility commands
        if args.test_device:
            success = test_device_connection(args.device)
            sys.exit(0 if success else 1)
        
        if args.screenshot:
            success = take_screenshot(args.device, args.screenshot)
            sys.exit(0 if success else 1)

        if args.restart_game:
            success = restart_pokemon_go(args.device)
            sys.exit(0 if success else 1)
        
        # Main operation modes
        if args.continuous:
            run_continuous_mode(
                args.device,
                args.adventure,
                args.max_sessions,
                args.session_delay
            )
        else:
            # Single session mode
            if not args.user or not args.password:
                logger.error("Username and password required for single session mode")
                sys.exit(1)
            
            success = run_single_session(
                args.device,
                args.user,
                args.password,
                args.adventure
            )
            
            sys.exit(0 if success else 1)
    
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Fatal error: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
