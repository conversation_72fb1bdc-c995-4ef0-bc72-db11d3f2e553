# -*- coding: utf-8 -*-
"""
Created on Sun Dec 15 01:22:31 2024

@author: andre
"""
import requests
from mongo_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>p<PERSON><PERSON>, <PERSON><PERSON>_Updater, <PERSON>goUp<PERSON><PERSON>,New<PERSON>ser
from adb_handler import AD<PERSON><PERSON><PERSON><PERSON>
from ocr_handler import OCRHandler
from image_detection import ImageDetector
from screenshot_handler import ScreenshotHandler
import time
import os
import cv2
import numpy as np
from cambiar_ip import CambiarIP  # Importa la clase desde cambiar_ip.py
from device_controller import MobileDeviceController  # Asumiendo que el archivo se llama device_controller.py

class UserManager:
   def __init__(self, device_id):
     self.adb_handler = ADBHandler(device_id)
     self.ocr_handler = OCRHandler()
     self.image_detector = ImageDetector()
     self.image_pather = ScreenshotHandler(device_id)
     self.device_id=device_id

   def change_name(self, user):
         """Changes the player name in the game."""
         self._menu() #simular click en la pantalla
         self._cam_nomb(user) #click in cambio de nombre
         user_instance = Mongo<PERSON><PERSON>y(user)
         if not user_instance.query_user(): #buscar si el user existe
            self._cambiar_nombre(user)
            Mongo_Updater(user).update_document()
            return True
        
   def get_screen_tet():
       screenshot_array = self.adb_handler.get_cv2_image()
       return self.ocr_handler.get_text(screenshot_array)
   def get_screen_coords(self,x1,y1,x2,y2):
       screenshot_array = self.adb_handler.get_cv2_image()
       return self.ocr_handler.get_text_from_region(screenshot_array, x1, y1, x2, y2) 
   
   def get_screen_text_coord(self,text):
        screenshot_array = self.adb_handler.get_cv2_image()       
        return self.ocr_handler.get_text_coordinates(screenshot_array,text)
   def get_screen_text(self):
       screenshot_array = self.adb_handler.get_cv2_image()
       return self.ocr_handler.get_text(screenshot_array)
   def screenshot_handler(self):
        return self.image_pather.capture()
   def _cam_nomb(self,user):
        """Verifica el nombre de user."""
        while not self.image_detector.detect(self.adb_handler.get_cv2_image(),'pkb.png') :
            self._menu()
        user_instance = MongoQuery(user)
        if not user_instance.query_user():
             self._cambiar_nombre(user)
             Mongo_Updater(user).update_document()
             self._menu()
             self.adb_handler.shell('input keyevent 4')
             self.adb_handler.shell('input keyevent 4')
             return True
        return False
   def _cambiar_nombre(self, user):
            """Handles the click sequence for changing the name."""
            def click_sequence(clicks, sleep_time=2):
               for coords in clicks:
                   self.click(*coords)
                   time.sleep(sleep_time)

            self.click_img('pkb.png')
            time.sleep(5)
            self.click_img("settings.png")
            time.sleep(5)
            click_sequence([(500, 1042), (960, 400), (500, 1300)])
            self.adb_handler.shell(f'input text "{user}"')
            time.sleep(5)
            self.adb_handler.shell('input keyevent 66')
            time.sleep(5)
            click_sequence([(500, 1300), (500, 1100), (500, 1200), (500, 1300), (500, 1700)])
            time.sleep(5)
            click_sequence([(500, 1100), (500, 1200), (500, 1300), (500, 1200)])

   def initial_setup(self):
        """Performs initial in-game setup steps."""
        self.click_img("ok.png")
        self.click_img("pasajero.png")
        self.click_img("pasenger.png")
        text=self.ocr_handler.get_text(self.adb_handler.get_cv2_image())
        if "Trading" in text:
          self.adb_handler.shell('input keyevent 4')

        if self.image_detector.detect(self.adb_handler.get_cv2_image(), "detais.png"):
              self.adb_handler.shell('input keyevent 4')
        if self.image_detector.detect(self.adb_handler.get_cv2_image(),"detailss.png"):
               self.adb_handler.shell('input keyevent 4')
        if self.image_detector.detect(self.adb_handler.get_cv2_image(),"rewards.png"):
               self.adb_handler.shell('input keyevent 4')

        self.click_img("passenger.png")
        self.click_img("pasenger.png")

        if self.image_detector.detect(self.adb_handler.get_cv2_image(),"referal.png"):
                self.click(551,1749)
                time.sleep(5)
                self.click(551,650)
                time.sleep(5)
                self.adb_handler.shell('input text 48bkj6wrr')
                time.sleep(5)
                self.click(551,231)
                time.sleep(5)
                self.click(551,1724)
                self.adb_handler.shell('input keyevent 4')
                self.click(551,1283)
        if self.image_detector.detect(self.adb_handler.get_cv2_image(), "continue.png"):
             self.adb_handler.shell('input keyevent 4')

   def close_session(self):
         self._restort("retrt")
   def get_level(self):
     x1, y1 = 326, 1508  # Top-left corner
     x2, y2 = 388, 1574  # Bottom-right corner
     image=self.adb_handler.screencap()
     screenshot_array = cv2.imdecode(np.frombuffer(image, dtype=np.uint8), cv2.IMREAD_COLOR)
     text = self.ocr_handler.get_text_from_region(screenshot_array, x1, y1, x2, y2)
     import re
     numeros_encontrados = re.findall(r'\d+', text)
     if numeros_encontrados:
         numero = int(numeros_encontrados[0])
         return numero
     return 0
   def close_session_(self):
     """Closes the game session by going to settings and closing."""
     self._menu()
     def tarea_1():
         exito = self.click_img("pkb.png")
         return exito
     def tarea_2():
          exito = self.click_img("settings.png")
          return exito
     def tarea_3():
       self.adb_handler.shell('input swipe 550 918 550 10 200')
       self.click(500,1500)
       time.sleep(1)
       self.click(500,1000)
       return None # Supongamos que exito_tarea_1 indica si la tarea se ejecutó con éxito

     tareas = [tarea_1, tarea_2, tarea_3]

     for i, tarea in enumerate(tareas):
        exito = tarea()
        if exito == None:
              print(f"Tarea {i+1} completed successful.")
        else:
              print(f"Tarea {i+1} falied. stop execution.")
              self.adb_handler.shell('am force-stop com.nianticlabs.pokemongo 1')
              self.adb_handler.shell('monkey -p com.nianticlabs.pokemongo 1')
              time.sleep(30)
              break
        time.sleep(5)


   def log_(self,user,pas):
       print("iniciar cuenta2")
       time.sleep(6)
       #self.adb_handler.shell('input keyevent 61')

       time.sleep(2)

       self.adb_handler.shell(f'input text "{user}"')
       self.adb_handler.shell('input keyevent 61')
       print(pas)
       self.adb_handler.shell(f'input text "{pas}"')
       print(user)
       
       #self.click_img("sign.png")
       self.adb_handler.shell('input keyevent 66')
       time.sleep(20)




   def _log_(self,user,pas):
       print("iniciar cuenta")
       time.sleep(6)
       #self.adb_handler.shell('input keyevent 61')

       time.sleep(2)

       self.adb_handler.shell(f'input text "{user}"')
       self.adb_handler.shell('input keyevent 61')
       print(pas)
       self.adb_handler.shell(f'input text "{pas}"')

       
       #self.click_img("sign.png")
       self.adb_handler.shell('input keyevent 66')
       time.sleep(20)
       start_time = time.time()
       # Captura el tiempo inicial
       while True:
            
           
            
            print("se supone ya inicio esperando si entra")
            text=self.ocr_handler.get_text(self.adb_handler.get_cv2_image())
            print(text)
            if "Recover" in text:
                 self.adb_handler.shell('input keyevent 4') 
                 self.adb_handler.shell('input keyevent 4') 
                 self.adb_handler.shell('input keyevent 4') 
            if "Forgot" in text:
                 self.adb_handler.shell('input keyevent 4') 
            if "Unable to sign in" in text:
                return False
            if "Forgot Log in" in text:
                self.adb_handler.shell('input keyevent 4')
                text = self.ocr_handler.get_text(self.adb_handler.get_cv2_image())

            if "Failed to Sign In" in text:
                self.adb_handler.shell('input keyevent 4')
                text = self.ocr_handler.get_text(self.adb_handler.get_cv2_image())

            if "Log in" in text:
                
               self.log_(user, pas)
               time.sleep(5) 
               text=self.ocr_handler.get_text(self.adb_handler.get_cv2_image())
               
            if "Error 15" in text:
                time.sleep(5) 
                MobileDeviceController(self.device_id).handle_errors()
                time.sleep(5) 
                text=self.ocr_handler.get_text(self.adb_handler.get_cv2_image())
                
            if "Powered by imperva" in text:
                time.sleep(5) 
                MobileDeviceController(self.device_id).handle_errors()
                time.sleep(5) 
                text=self.ocr_handler.get_text(self.adb_handler.get_cv2_image())                
                
            if "Access denied"  in text:
                self.adb_handler.shell("content insert --uri content://settings/system --bind name:s:accelerometer_rotation --bind value:i:0")
                self.adb_handler.shell("content insert --uri content://settings/system --bind name:s:user_rotation --bind value:i:0")

            
            if  "DISMISS" in text:
                print("entro text dismis")
                return True
            
            
            if  "internet" in text:
                MobileDeviceController(self.device_id).handle_errors()
                text = self.ocr_handler.get_text(self.adb_handler.get_cv2_image())

            if "Stay" in text:
                print("entro text Stay")
                return True
            if self.image_detector.detect(self.adb_handler.get_cv2_image(), "pkbss10.png"):
                return True 
            if "REWARDS" in text:
                print("entro text rewards")
                return True
            # Comprueba si han pasado 30 segundos
            if "We are unable to log" in text:
                with open(r"c:\\temp\cuentas.txt", 'a') as file:
                 file.write(f"{user}:{pas}\n")
                 return False
            if "unable" in text:
                 with open(r"c:\\temp\cuentas.txt", 'a') as file:
                  file.write(f"{user}:{pas}\n")
                  return False 
              
            if "password is incorrect" in text:
                     with open(r"c:\\temp\cuentas.txt", 'a') as file:
                      file.write(f"{user}:{pas} incorrect\n")
                      return False     
            if "NEW PLAYER" in text:
             self.click(350,900)
             self.click(350,900)
            if "RETURNING" in text:#self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
                            self.click(327, 880)
                            self.click(327, 880)   
            #if time.time() - start_time > 60000:
            #   return False 
            time.sleep(5) 
            text = self.ocr_handler.get_text(self.adb_handler.get_cv2_image())

   def _route_user_pass(self, user,pas):
     time.sleep(5)  
     print(self.ocr_handler.get_text(self.adb_handler.get_cv2_image()))
     self._log_(user,pas)
     return True

   def proxy_quit(self):
       
            self.adb_handler.shell("adb shell settings delete global http_proxy")
            self.adb_handler.shell("adb shell settings delete global global_http_proxy_host")
            self.adb_handler.shell("adb shell settings delete global global_http_proxy_port")
            self.adb_handler.shell("adb shell settings delete global global_http_proxy_username")
            self.adb_handler.shell("adb shell settings delete global global_http_proxy_password")
            print("[INFO] Configuración de proxy eliminada.")
   def proxy(self):
       
        import os
        import random
        
        # Lista de proxies manualmente proporcionada
        PROXIES = [
            "**************:5868:eamhfqsx:mif7qnbylsbo",
            "**************:9594:eamhfqsx:mif7qnbylsbo",
            "*************:6630:eamhfqsx:mif7qnbylsbo",
            "*************:6641:eamhfqsx:mif7qnbylsbo",
            "***************:6360:eamhfqsx:mif7qnbylsbo",
            "*************:6837:eamhfqsx:mif7qnbylsbo",
            "*************:6732:eamhfqsx:mif7qnbylsbo",
            "***************:6655:eamhfqsx:mif7qnbylsbo",
            "************:5735:eamhfqsx:mif7qnbylsbo",
            "45.151.162.198:6600:eamhfqsx:mif7qnbylsbo"
        ]
        
        # Configurar proxy en Android mediante ADB
        def set_proxy():
            """Configura un proxy aleatorio de la lista en el dispositivo Android usando ADB."""
            if not PROXIES:
                print("[ERROR] No hay proxies disponibles.")
                return
            
            proxy = random.choice(PROXIES)  # Seleccionar un proxy aleatorio
            parts = proxy.split(":")
            if len(parts) != 4:
                print(f"[ERROR] Formato de proxy incorrecto: {proxy}")
                return
            
            ip, port, user, password = parts
            print(f"[INFO] Configurando proxy: {ip}:{port} con usuario {user}")
            
            self.adb_handler.shell(f"adb shell settings put global http_proxy {ip}:{port}")
            self.adb_handler.shell(f"adb shell settings put global global_http_proxy_host {ip}")
            self.adb_handler.shell(f"adb shell settings put global global_http_proxy_port {port}")
            self.adb_handler.shell(f"adb shell settings put global global_http_proxy_username {user}")
            self.adb_handler.shell(f"adb shell settings put global global_http_proxy_password {password}")
            print("[INFO] Proxy configurado correctamente en el dispositivo.")
        
        set_proxy()# Eliminar configuración de proxy en Android


       
      #self.adb_handler.shell(f'settings put global http_proxy {random.choice(proxies)}')
   
   def bk(self, rr):
        """bk player preferences from a backup."""

        dest = os.path.join("c:/temp", rr)
        self.adb_handler.shell(f"mkdir /sdcard/acc/ -p")
        self.adb_handler.shell(f"su --mount-master -c 'cp /data/data/com.nianticlabs.pokemongo/shared_prefs/com.nianticlabs.pokemongo.v2.playerprefs.xml /sdcard/acc/{rr}'")
        self.adb_handler.pull(f'/sdcard/acc/{rr}', dest)

   def cookies(self):
        self.adb_handler.shell(f'su -c "rm -rf /data/data/com.android.chrome/app_chrome/Default/History*"')
            
            # Eliminar cookies
        self.adb_handler.shell(f' su -c "rm -rf /data/data/com.android.chrome/app_chrome/Default/Cookies*"')
            
            # Eliminar caché de Chrome
        self.adb_handler.shell(f'su -c "rm -rf /data/data/com.android.chrome/cache/*"')
        self.adb_handler.shell(f'su -c "rm -rf /data/data/com.android.chrome/app_cache/*"')
            
            # Eliminar datos de sesión (pestañas abiertas)
        self.adb_handler.shell(f' su -c "rm -rf /data/data/com.android.chrome/app_chrome/Default/Sessions/*"')     
   def _restort(self, user,pas):
        """Restores player preferences from a backup."""
        self.adb_handler.shell(f'su -c "rm -rf /data/data/com.android.chrome/app_chrome/Default/History*"')
            
            # Eliminar cookies
        self.adb_handler.shell(f' su -c "rm -rf /data/data/com.android.chrome/app_chrome/Default/Cookies*"')
            
            # Eliminar caché de Chrome
        self.adb_handler.shell(f'su -c "rm -rf /data/data/com.android.chrome/cache/*"')
        self.adb_handler.shell(f'su -c "rm -rf /data/data/com.android.chrome/app_cache/*"')
            
            # Eliminar datos de sesión (pestañas abiertas)
        self.adb_handler.shell(f' su -c "rm -rf /data/data/com.android.chrome/app_chrome/Default/Sessions/*"')
            
            # Reiniciar Chrome para aplicar cambios
        self.adb_handler.shell(f' su -c "am force-stop com.android.chrome"')
        self.adb_handler.shell('am force-stop com.nianticlabs.pokemongo 1')
        self.adb_handler.shell('am force-stop com.nianticlabs.pokemongo 1')
        dest = os.path.join("c:/temp/", user)
        if not os.path.exists(dest):
            print("file dont exist")
            dest = os.path.join("c:/temp/retrt")
            self.adb_handler.shell('am force-stop com.nianticlabs.pokemongo 1')
            self.adb_handler.shell("su --mount-master -c 'rm -rf  /data/data/com.nianticlabs.pokemongo/shared_prefs/com.nianticlabs.pokemongo.v2.playerprefs.xml'")
            self.adb_handler.shell(f"mkdir /sdcard/acc/ -p")
            self.adb_handler.push(dest, f'/sdcard/acc/retrt')
            self.adb_handler.shell(f"su --mount-master -c 'cp /sdcard/acc/retrt  /data/data/com.nianticlabs.pokemongo/shared_prefs/com.nianticlabs.pokemongo.v2.playerprefs.xml'")
           
            self._reini(user)
        else:    
            print("restored playerprefs")
            self.adb_handler.shell(f"mkdir /sdcard/acc/ -p")
            self.adb_handler.push(dest, f'/sdcard/acc/{user}')
            self.adb_handler.shell(f"su --mount-master -c 'cp /sdcard/acc/{user}  /data/data/com.nianticlabs.pokemongo/shared_prefs/com.nianticlabs.pokemongo.v2.playerprefs.xml'")
            self._reini(user)
        print("######")
        #time.sleep(3)

        while True:
             text=self.ocr_handler.get_text(self.adb_handler.get_cv2_image())
             print(text)
             if "Recover" in text:
                  self.adb_handler.shell('input keyevent 4') 
                  self.adb_handler.shell('input keyevent 4') 
                  self.adb_handler.shell('input keyevent 4') 
             if "Forgot" in text:
                  self.adb_handler.shell('input keyevent 4') 
             if "unable" in text:
                  with open(r"c:\\temp\cuentas.txt", 'a') as file:
                   file.write(f"{user}:{pas}\n")
                   return False 
             if "DISMISS" in text:
                 return True
             if "Error 15" in text:
                time.sleep(5) 
                MobileDeviceController(self.device_id).handle_errors()
                #self._reini(user)
                #self.proxy()
                #CambiarIP()
                self._reini(user)
                         
             if  "internet" in text:
                MobileDeviceController(self.device_id).handle_errors()
   
             if "Choose" in text   :
                 
              self.adb_handler.shell('input keyevent 4') 
              self.click(372, 910) 
             if "Stay" in text:
                  return True

             if self.image_detector.detect(self.adb_handler.get_cv2_image(), "pkbss10.png"):
                return True

             if "REWARDS" in text:
                 return True
             if "Failed" in text:
                 self.click(372, 910)         
                 #return self._route_user_pass(user,pas)
                 
             if "Log" in text:#self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
                
                return self._log_(user,pas)
            
             if "NEW PLAYER" in text:#self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
                   self.click(327, 880)
                   self.click(327, 880)
             if "RETURNING" in text:#self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
                         self.click(327, 880)
                         self.click(327, 880)      
             if "TRY ADIFFERENT ACCOUNT" in text:#self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
                   self.click(372, 910)
             if "SUBMIT" in text:#self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
                  self.click(572, 868)
                  for _ in range(80):  # 50 pulsaciones rápidas (~5 segundos)
                      self.adb_handler.shell(f'input keyevent 20')  # Tab key to navigate to the username field
                      self.adb_handler.shell(f'input keyevent 20')  # Tab key to navigate to the username field
              
                  self.adb_handler.shell(f'input keyevent 66') 
                  self.click(351, 1000)
                  
             #if "Failed to Sign In" in text:#self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
             #     return False
   def _reini(self,user):
        """Restarts the game app"""
        print("poly restart app")
        self.adb_handler.shell("content insert --uri content://settings/system --bind name:s:accelerometer_rotation --bind value:i:0")
        self.adb_handler.shell("content insert --uri content://settings/system --bind name:s:user_rotation --bind value:i:0")

        self.adb_handler.shell('am force-stop com.nianticlabs.pokemongo 1')
        self.adb_handler.shell('monkey -p com.nianticlabs.pokemongo 1')
        time.sleep(2)
        self.adb_handler.shell("content insert --uri content://settings/system --bind name:s:accelerometer_rotation --bind value:i:0")
        self.adb_handler.shell("content insert --uri content://settings/system --bind name:s:user_rotation --bind value:i:0")

        #self.click_img("noth.png")

   def _reini_c(self, user):
        """Attempts to restart game with a failsafe method."""
        print("sesion, esperando 10 sec")
        time.sleep(10)
        n=0
        while not self.image_detector.detect(self.adb_handler.get_cv2_image(),'return.png'):
              text=self.ocr_handler.get_text(self.adb_handler.get_cv2_image())
              if "log" in text:
                  return
              self.click(528, 1373)
              self.click(528, 1332)
              self.click_img("noth.png")
              self.initial_setup()
              n=n+1
              if n==5:
                  #self.adb_handler.shell('am force-stop com.nianticlabs.pokemongo 1')
                  #self.adb_handler.shell('monkey -p com.nianticlabs.pokemongo 1')
                  self._reini(user)
                  time.sleep(20)
                  n=0
              print("sesion, if find player return ")
              if self.image_detector.detect(self.adb_handler.get_cv2_image(),"pkb.png"):
                 print(f"session, find pokeball  close session")
                 self.close_session()
              if self.image_detector.detect(self.adb_handler.get_cv2_image(),"gim.png"):
                 self.adb_handler.shell('input keyevent 4')
              time.sleep(1)

        self.click(528, 1373)
        self.click(528, 1332)
        self.click_img("return.png")

        while not self.image_detector.detect(self.adb_handler.get_cv2_image(),'club.png'):
             time.sleep(1)
   def _menu(self):
        """Executes menu-related actions, such as closing dialogs or popups."""
        screenshot_array = self.adb_handler.get_cv2_image()
        actions = ["textopokestop", "lets", "pasajero", "salir", "pasajero",
                   "pasenger", "passenger", "pasenger", "continue", "skip",
                   "hey","cance", "dismiss","oks10"]
        for action in actions:
            if action == "oks10":
                text = self.ocr_handler.get_text(self.adb_handler.get_cv2_image())
                if "exit" in text:
                   self.adb_handler.shell('input keyevent 4')
            else:            
             self.click_img(f'{action}.png',screenshot_array)
        self._handle_fail_conditions(screenshot_array)
        if self.image_detector.detect(screenshot_array,"details10.png"):
              self.adb_handler.shell('input keyevent 4')
        if self.image_detector.detect(screenshot_array,"detais.png"):
              self.adb_handler.shell('input keyevent 4')
        if self.image_detector.detect(screenshot_array, "rewards.png"):
            self.adb_handler.shell('input keyevent 4')
        if self.image_detector.detect(screenshot_array, "walk.png"):
            self.adb_handler.shell('input keyevent 4')
            self.adb_handler.shell('input keyevent 4')
            self.adb_handler.shell('input keyevent 4')
            self.adb_handler.shell('input keyevent 4')
          
    
        text = self.ocr_handler.get_text(self.adb_handler.get_cv2_image())
        if "exit" in text:
                self.adb_handler.shell('input keyevent 4')
        print("fin menu")
   def _handle_fail_conditions(self, screenshot_array):
      """Handles in-game fail states based on recognized text"""
      fail_text_actions = [
          ("Weather", lambda: self.click(350, 1028)),
         ("NIANTIC ID", lambda: self.adb_handler.shell('input keyevent 4') or self.adb_handler.shell('input keyevent 4')),
         ("Trade", lambda: self.click(500, 1600)),
         ("friend ", lambda: self.click(500, 1940)),
          ("there ", lambda: self.click(500, 1940)),
          ("Hey", lambda: self.adb_handler.shell('input keyevent 4')),
           ("DISMISS", lambda: self.adb_handler.shell('input keyevent 4')),
          ("friends", lambda: self.click(650, 1909)),
          ("Unlocked", lambda: self.click(550, 1640)),
          ("CONTINUE", lambda: self.click(550, 1900)),
          
           ("Trading", lambda: self.adb_handler.shell('input keyevent 4')),
          ("SEE DETAILS", lambda: self.adb_handler.shell('input keyevent 4')),
           ("Friend", lambda: self.adb_handler.shell('input keyevent 4')),
           ("Buddy", lambda: self.adb_handler.shell('input keyevent 4')),
           ("Please enter", lambda: self.click(777, 1273) and  self.click(777, 1273) and self.click(777, 1450))

      ]
      text = self.ocr_handler.get_text(screenshot_array)
      for text_condition, action in fail_text_actions:
           if text_condition in text:
              action()
       
   def click(self,x,y):
      self.adb_handler.shell(f'input tap {x} {y}')
      time.sleep(1)
   def click_img(self, tipo,screenshot_array=None):
       """Clicks on the location of the identified image."""
       if screenshot_array is None:
        screenshot_array =self.adb_handler.get_cv2_image()
       if template_path:= self.image_detector.detect(screenshot_array, tipo):
         self.click(template_path[0], template_path[1])
         return True
       else:
          return False
   def budy(self):
       print("##buddy")
       print(self.ocr_handler.get_text_from_region(self.adb_handler.get_cv2_image(),175,171,533,249))
       if "buddy" in self.ocr_handler.get_text_from_region(self.adb_handler.get_cv2_image(),175,171,533,249):
               self.click(600, 530)
               self.click(600, 530)
               self.click(600, 530)
               self.click(600, 530)
               self.click(600, 530)
               self.click(600, 530)
       if "joined" in self.ocr_handler.get_text_from_region(self.adb_handler.get_cv2_image(),175,171,533,249):
                   self.click(600, 530)
                   self.click(600, 530)
                   self.click(600, 530)
                   self.click(600, 530)
                   self.click(600, 530)
                   self.click(600, 530) 
   def check_and_handle_error15(self):
       """Checks and handles error15 in the game login"""
       if self.image_detector.detect(self.adb_handler.get_cv2_image(),'error15.png'):
           #self._connect()
           self._reini(self.user)
           #self.click_img("club.png")
   def _connect(self):
      """connect to the VPN app"""
      self.adb_handler.shell(f'am force-stop  net.flashsoft.flashvpn.activity 1')
      self.adb_handler.shell("monkey -p net.flashsoft.flashvpn.activity -c android.intent.category.LAUNCHER 1")
      while True:
        print("probandooo")
        if self.image_detector.detect(self.adb_handler.get_cv2_image(),"vpncon.png"):
          print("connect")
          self.click(522,700)
          self.click(522,700)
          while True:
              if self.image_detector.detect(self.adb_handler.get_cv2_image(),"connected.png"):
                 return
        if self.image_detector.detect(self.adb_handler.get_cv2_image(),"discon_1.png"):
          print("disconnected")
          self.click(522,700)
          self.click(522,700)
          while True:
              if self.image_detector.detect(self.adb_handler.get_cv2_image(),"disconnected.png"):
                self.click(80,180)
                return
        if self.image_detector.detect(self.adb_handler.get_cv2_image(),"connected.png"):
            self.click(80,180)
            time.sleep(4)
            self.click(522,700)
            while True:
             if self.image_detector.detect(self.adb_handler.get_cv2_image(),"disconnected.png"):
                self.click(80,180)
                return
   def login(self,user, pas):
       """Logs the player into the game."""
       if not self.adb_handler.device:
         return
       self.user=user #asginacion del user
       if self._restort(user,pas): #busca si el user tiene
           return True
       else:
           return False
       print(f"session, restart app")
       #self.check_and_handle_error15()

       time.sleep(5)
       print("entro")
       text = self.ocr_handler.get_text(self.adb_handler.get_cv2_image())

       if "seeing" in text:
            self.adb_handler.shell('input keyevent 4')
       if "Recover" in text:
            self.adb_handler.shell('input keyevent 4') 
            self.adb_handler.shell('input keyevent 4') 
            self.adb_handler.shell('input keyevent 4') 
       if "Forgot" in text:
            self.adb_handler.shell('input keyevent 4') 

       if "Customer" in text:
            
             print("ban")
             return False
       if "ok" in text:
            return True
       if "unable" in text:
           return False
       if "Failed" in text:
           self.click(550,1280)
           time.sleep(10)

       if "Fail" in text:
          
          self.click(550,1280)
          time.sleep(10)
       
       if "date" in text:
           self.close_session()
       if "requesting" in text:
              up=NewUser(user)
              up.update_document()
              return False
          


       if "have an account" in  text:
           
          time.sleep(6)
           
          self.adb_handler.shell('input keyevent 61')
          
          time.sleep(2)

          self.adb_handler.shell(f'input text "{user}"')
          self.adb_handler.shell('input keyevent 61')

          self.adb_handler.shell(f'input text "{pas}"')

          
          self.click_img("sign.png")
          self.click_img("hey.png")
          self.adb_handler.shell('input keyevent 66')
          time.sleep(10)
          text = self.ocr_handler.get_text(self.adb_handler.get_cv2_image())

       if "log" in text:
            time.sleep(6)            
            self.adb_handler.shell('input keyevent 61')
            
            time.sleep(2)

            self.adb_handler.shell(f'input text "{user}"')
            self.adb_handler.shell('input keyevent 61')
          
            self.adb_handler.shell(f'input text "{pas}"')
          
            
            self.click_img("sign.png")
            self.click_img("hey.png")
            self.adb_handler.shell('input keyevent 66')
            time.sleep(10)
            text = self.ocr_handler.get_text(self.adb_handler.get_cv2_image())

            if self.image_detector.detect(self.adb_handler.get_cv2_image(),'logmal.png'):
                return False
            x=0
            while x<40:

                self.click_img("hey.png")
                self.click_img("ok.png")
                self.click_img("cance.png")

                if self.image_detector.detect(self.adb_handler.get_cv2_image(),'accpt.png'):
                  self.click(550,1140)
                  time.sleep(2)
                  self.click(550,1140)

                if "items" in  self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
                    self.click(550,1350)
                    time.sleep(2)
                    self.click(550,1350)
                    time.sleep(2)
                    self.click(550,1350)
                    time.sleep(2)
                    self.click(550,1350)
                    time.sleep(2)
                    self.click(550,1280)

                if   "requesting" in self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
                     up=NewUser(user)
                     up.update_document()
                     return False
                if "Fail" in  self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):

                   self.click(550,1280)
                   time.sleep(10)
                if  "to the following" in   self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
                     up=NewUser(user)
                     up.update_document()
                     return False
                if  "to the following" in   self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
                     up=NewUser(user)
                     up.update_document()
                     return False

                if  "Internal server error" in self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
                      print("ban")
                      return False
                if   "Failed" in   self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
                    self.click(550,1280)
                    time.sleep(10)
                    print("ban")
                    return False
                if x==10:
                    self._menu()
                x=x+1
                print(x,"pokebal?")

                if  self.image_detector.detect(self.adb_handler.get_cv2_image(),'pkbs10.png'):
                   return True
            if self.image_detector.detect(self.adb_handler.get_cv2_image(),'niantic.png'):
                      self._reini(user)
            return False
       print("return busc")

       while not "NEW PLAYER" in self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
          if "to your" in  self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
              break
          time.sleep(3)
          if  self.image_detector.detect(self.adb_handler.get_cv2_image(),'pkbs10.png'):
           return True
          self._menu()

          if self.image_detector.detect(self.adb_handler.get_cv2_image(),'niantic.png'):
                     self._reini(user)

       self.click_img("return.png")
       self.click_img("club.png")
       time.sleep(3)


       while not "to your" in self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
          print("aqui")
          
          if self.image_detector.detect(self.adb_handler.get_cv2_image(),'error15.png'):
            self._connect()
            self._reini(user)

            self.click_img("club.png")

          time.sleep(5)
       print("entro??")
        
       time.sleep(3)
       self.adb_handler.shell('input keyevent 61')

       time.sleep(2)

       self.adb_handler.shell(f'input text "{user}"')
       self.adb_handler.shell('input keyevent 61')
       
       self.adb_handler.shell(f'input text "{pas}"')

       
       self.click_img("sign.png")
       self.adb_handler.shell('input keyevent 66')
       time.sleep(20)
       self._reini(user)
       if self.image_detector.detect(self.adb_handler.get_cv2_image(),'logmal.png'):
           return False
       x=0
       while x<40:

           if  self.image_detector.detect(self.adb_handler.get_cv2_image(),'niantic.png'):
                 self._reini(user)
           self.click_img("hey.png")
           self.click_img("ok.png")
           self.click_img("cance.png")

           if "account" in self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
                return False

           if self.image_detector.detect(self.adb_handler.get_cv2_image(),'accpt.png'):
                self.click(550,1150)
                time.sleep(2)
                self.click(550,1150)


           if "items" in self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):

               self.click(550,1150)
               time.sleep(2)
               self.click(550,1150)
               time.sleep(2)
               self.click(550,1350)
               time.sleep(2)
               self.click(550,1350)
               time.sleep(2)
               self.click(550,1280)

           if "Fail" in   self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
              
               self.click(550,1280)
               time.sleep(10)

           if  "requesting" in  self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):
                 up=NewUser(user)
                 up.update_document()
                 return False
           if "are unable" in  self.ocr_handler.get_text(self.adb_handler.get_cv2_image()):

                 print("ban")
                 return False
           if x==10:
               self._menu()

           x=x+1
           print(x,"pokebal?")
           if  self.image_detector.detect(self.adb_handler.get_cv2_image(),'pkbs10.png'):
           
              return True

       return False
   
#print(UserManager("R28M22DWS4A").get_screen_coords(38,800,260,1100))