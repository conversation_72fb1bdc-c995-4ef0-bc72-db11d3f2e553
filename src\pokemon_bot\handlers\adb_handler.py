"""Refactored ADB handler with improved error handling and connection management."""

import time
from typing import Optional, Union, List
import numpy as np
import cv2
from ppadb.client import Client
from ppadb.device import Device

from ..core.config import get_config
from ..core.exceptions import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rror, TimeoutError
from ..core.logging import get_logger
from ..core.retry import retry, CircuitBreaker

logger = get_logger(__name__)


class ADBHandler:
    """Enhanced ADB handler with connection pooling and error recovery."""
    
    def __init__(self, device_id: str):
        """Initialize ADB handler.
        
        Args:
            device_id: Android device identifier (IP:port or serial)
        """
        self.device_id = device_id
        self.config = get_config()
        self._client: Optional[Client] = None
        self._device: Optional[Device] = None
        self._connection_circuit_breaker = CircuitBreaker(
            failure_threshold=3,
            recovery_timeout=30.0,
            expected_exception=ADBError
        )
        
        logger.add_device_context(device_id)
        self._connect()
    
    def _connect(self) -> None:
        """Establish ADB connection."""
        try:
            self._client = Client(
                host=self.config.adb.host,
                port=self.config.adb.port
            )
            
            self._device = self._client.device(self.device_id)
            if not self._device:
                raise ADBError(
                    f"Device {self.device_id} not found",
                    error_code="DEVICE_NOT_FOUND"
                )
            
            # Test connection
            self._device.shell("echo 'connection_test'", timeout=5)
            logger.info(f"Successfully connected to device {self.device_id}")
            
        except Exception as e:
            raise ADBError(
                f"Failed to connect to device {self.device_id}: {e}",
                error_code="CONNECTION_FAILED"
            ) from e
    
    @property
    def device(self) -> Device:
        """Get the ADB device instance."""
        if not self._device:
            self._connect()
        return self._device
    
    def is_connected(self) -> bool:
        """Check if device is connected."""
        try:
            if not self._device:
                return False
            self._device.shell("echo 'ping'", timeout=2)
            return True
        except Exception:
            return False
    
    @retry(max_attempts=3, exceptions=(ADBError, TimeoutError))
    def shell(self, command: str, timeout: Optional[int] = None) -> str:
        """Execute shell command on device.
        
        Args:
            command: Shell command to execute
            timeout: Command timeout in seconds
            
        Returns:
            Command output
            
        Raises:
            ADBError: If command execution fails
        """
        if timeout is None:
            timeout = self.config.adb.command_timeout
        
        try:
            if not self.is_connected():
                logger.warning("Device disconnected, attempting reconnection")
                self._connect()
            
            logger.log_device_action("shell_command", self.device_id, command=command)
            result = self.device.shell(command, timeout=timeout)
            
            return result or ""
            
        except Exception as e:
            raise ADBError(
                f"Shell command failed: {command}",
                error_code="SHELL_COMMAND_FAILED",
                details={"command": command, "error": str(e)}
            ) from e
    
    @retry(max_attempts=2, exceptions=ADBError)
    def tap(self, x: int, y: int, duration: int = 100) -> None:
        """Tap at coordinates.
        
        Args:
            x: X coordinate
            y: Y coordinate  
            duration: Tap duration in milliseconds
        """
        command = f"input tap {x} {y}"
        if duration != 100:
            command = f"input touchscreen swipe {x} {y} {x} {y} {duration}"
        
        self.shell(command)
        logger.log_device_action("tap", self.device_id, x=x, y=y, duration=duration)
    
    def swipe(self, x1: int, y1: int, x2: int, y2: int, duration: int = 300) -> None:
        """Swipe between coordinates.
        
        Args:
            x1, y1: Start coordinates
            x2, y2: End coordinates
            duration: Swipe duration in milliseconds
        """
        command = f"input swipe {x1} {y1} {x2} {y2} {duration}"
        self.shell(command)
        logger.log_device_action("swipe", self.device_id, x1=x1, y1=y1, x2=x2, y2=y2, duration=duration)
    
    def input_text(self, text: str) -> None:
        """Input text on device.
        
        Args:
            text: Text to input
        """
        # Escape special characters
        escaped_text = text.replace('"', '\\"').replace("'", "\\'")
        command = f'input text "{escaped_text}"'
        self.shell(command)
        logger.log_device_action("input_text", self.device_id, text_length=len(text))
    
    def key_event(self, keycode: Union[int, str]) -> None:
        """Send key event.
        
        Args:
            keycode: Key code (number or name)
        """
        command = f"input keyevent {keycode}"
        self.shell(command)
        logger.log_device_action("key_event", self.device_id, keycode=keycode)
    
    def back(self) -> None:
        """Press back button."""
        self.key_event(4)
    
    def home(self) -> None:
        """Press home button."""
        self.key_event(3)
    
    def menu(self) -> None:
        """Press menu button."""
        self.key_event(82)
    
    @retry(max_attempts=3, exceptions=ADBError)
    def screencap(self) -> bytes:
        """Capture screenshot.
        
        Returns:
            Screenshot data as bytes
            
        Raises:
            ADBError: If screenshot capture fails
        """
        try:
            screenshot_data = self.device.screencap()
            if not screenshot_data:
                raise ADBError("Screenshot data is empty", error_code="EMPTY_SCREENSHOT")
            
            logger.log_device_action("screencap", self.device_id, data_size=len(screenshot_data))
            return screenshot_data
            
        except Exception as e:
            raise ADBError(
                "Failed to capture screenshot",
                error_code="SCREENCAP_FAILED"
            ) from e
    
    def get_cv2_image(self) -> np.ndarray:
        """Get screenshot as OpenCV image array.
        
        Returns:
            Screenshot as numpy array
            
        Raises:
            ADBError: If screenshot processing fails
        """
        try:
            screenshot_data = self.screencap()
            image_array = cv2.imdecode(
                np.frombuffer(screenshot_data, dtype=np.uint8),
                cv2.IMREAD_COLOR
            )
            
            if image_array is None:
                raise ADBError("Failed to decode screenshot", error_code="DECODE_FAILED")
            
            return image_array
            
        except ADBError:
            raise
        except Exception as e:
            raise ADBError(
                "Failed to process screenshot",
                error_code="SCREENSHOT_PROCESSING_FAILED"
            ) from e
    
    def pull(self, remote_path: str, local_path: str) -> None:
        """Pull file from device.
        
        Args:
            remote_path: Path on device
            local_path: Local destination path
        """
        try:
            self.device.pull(remote_path, local_path)
            logger.log_device_action("pull", self.device_id, remote_path=remote_path, local_path=local_path)
        except Exception as e:
            raise ADBError(f"Failed to pull file: {e}", error_code="PULL_FAILED") from e
    
    def push(self, local_path: str, remote_path: str) -> None:
        """Push file to device.
        
        Args:
            local_path: Local file path
            remote_path: Destination path on device
        """
        try:
            self.device.push(local_path, remote_path)
            logger.log_device_action("push", self.device_id, local_path=local_path, remote_path=remote_path)
        except Exception as e:
            raise ADBError(f"Failed to push file: {e}", error_code="PUSH_FAILED") from e
    
    def get_device_info(self) -> dict:
        """Get device information.
        
        Returns:
            Dictionary with device information
        """
        try:
            info = {
                "device_id": self.device_id,
                "model": self.shell("getprop ro.product.model").strip(),
                "android_version": self.shell("getprop ro.build.version.release").strip(),
                "sdk_version": self.shell("getprop ro.build.version.sdk").strip(),
                "manufacturer": self.shell("getprop ro.product.manufacturer").strip(),
                "screen_size": self._get_screen_size(),
                "connected": self.is_connected()
            }
            return info
        except Exception as e:
            logger.error(f"Failed to get device info: {e}")
            return {"device_id": self.device_id, "connected": False, "error": str(e)}
    
    def _get_screen_size(self) -> tuple:
        """Get device screen size."""
        try:
            output = self.shell("wm size")
            # Parse output like "Physical size: 1080x2340"
            if ":" in output:
                size_str = output.split(":")[-1].strip()
                width, height = map(int, size_str.split("x"))
                return (width, height)
        except Exception:
            pass
        return (0, 0)
    
    def disconnect(self) -> None:
        """Disconnect from device."""
        try:
            if self._client:
                # Note: ppadb doesn't have explicit disconnect
                self._client = None
                self._device = None
                logger.info(f"Disconnected from device {self.device_id}")
        except Exception as e:
            logger.error(f"Error during disconnect: {e}")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()

    def wait_for_element(self, element_check: callable, timeout: int = 30, interval: float = 1.0) -> bool:
        """Wait for an element to appear on screen.

        Args:
            element_check: Function that returns True when element is found
            timeout: Maximum wait time in seconds
            interval: Check interval in seconds

        Returns:
            True if element found, False if timeout
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                if element_check():
                    return True
            except Exception as e:
                logger.debug(f"Element check failed: {e}")

            time.sleep(interval)

        return False
