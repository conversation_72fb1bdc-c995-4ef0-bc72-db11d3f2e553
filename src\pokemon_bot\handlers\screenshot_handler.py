"""Enhanced screenshot handler with optimization and management."""

import cv2
import numpy as np
import tempfile
import os
from pathlib import Path
from typing import Optional, Union
import time

from ..core.config import get_config
from ..core.exceptions import ScreenshotError
from ..core.logging import get_logger
from .adb_handler import ADBHandler

logger = get_logger(__name__)


class ScreenshotHandler:
    """Enhanced screenshot handler with caching and optimization."""
    
    def __init__(self, device_id: str):
        """Initialize screenshot handler.
        
        Args:
            device_id: Android device identifier
        """
        self.device_id = device_id
        self.config = get_config()
        self.adb_handler = ADBHandler(device_id)
        
        # Setup temp directory
        self.temp_dir = Path(self.config.screenshot.temp_dir)
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        # Screenshot settings
        self.format = self.config.screenshot.format
        self.quality = self.config.screenshot.quality
        self.cleanup_after = self.config.screenshot.cleanup_after
        
        # Track created files for cleanup
        self._created_files = []
        self._last_cleanup = time.time()
    
    def capture(self, save_path: Optional[Union[str, Path]] = None) -> str:
        """Capture screenshot and save to file.
        
        Args:
            save_path: Optional path to save screenshot
            
        Returns:
            Path to saved screenshot file
            
        Raises:
            ScreenshotError: If screenshot capture fails
        """
        try:
            # Get screenshot data from device
            screenshot_data = self.adb_handler.screencap()
            
            # Convert to OpenCV image
            image_array = cv2.imdecode(
                np.frombuffer(screenshot_data, dtype=np.uint8),
                cv2.IMREAD_COLOR
            )
            
            if image_array is None:
                raise ScreenshotError(
                    "Failed to decode screenshot data",
                    error_code="DECODE_FAILED"
                )
            
            # Determine save path
            if save_path:
                file_path = Path(save_path)
            else:
                timestamp = int(time.time() * 1000)
                filename = f"{self.device_id}_{timestamp}.{self.format.lower()}"
                file_path = self.temp_dir / filename
            
            # Ensure directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save image
            if self.format.upper() == "JPEG":
                cv2.imwrite(
                    str(file_path),
                    image_array,
                    [cv2.IMWRITE_JPEG_QUALITY, self.quality]
                )
            else:
                cv2.imwrite(str(file_path), image_array)
            
            # Track file for cleanup
            if not save_path:  # Only track temp files
                self._created_files.append((str(file_path), time.time()))
            
            # Periodic cleanup
            self._cleanup_old_files()
            
            logger.debug(f"Screenshot saved: {file_path}")
            return str(file_path)
            
        except ScreenshotError:
            raise
        except Exception as e:
            raise ScreenshotError(
                f"Failed to capture screenshot: {e}",
                error_code="CAPTURE_FAILED"
            ) from e
    
    def capture_region(
        self,
        x1: int, y1: int, x2: int, y2: int,
        save_path: Optional[Union[str, Path]] = None
    ) -> str:
        """Capture screenshot of specific region.
        
        Args:
            x1, y1, x2, y2: Region coordinates
            save_path: Optional path to save screenshot
            
        Returns:
            Path to saved screenshot file
        """
        try:
            # Get full screenshot
            image_array = self.adb_handler.get_cv2_image()
            
            # Crop to region
            cropped = image_array[y1:y2, x1:x2]
            
            # Determine save path
            if save_path:
                file_path = Path(save_path)
            else:
                timestamp = int(time.time() * 1000)
                filename = f"{self.device_id}_region_{timestamp}.{self.format.lower()}"
                file_path = self.temp_dir / filename
            
            # Save cropped image
            cv2.imwrite(str(file_path), cropped)
            
            # Track file for cleanup
            if not save_path:
                self._created_files.append((str(file_path), time.time()))
            
            logger.debug(f"Region screenshot saved: {file_path}")
            return str(file_path)
            
        except Exception as e:
            raise ScreenshotError(
                f"Failed to capture region screenshot: {e}",
                error_code="REGION_CAPTURE_FAILED"
            ) from e
    
    def capture_to_memory(self) -> np.ndarray:
        """Capture screenshot directly to memory without saving.
        
        Returns:
            Screenshot as numpy array
        """
        return self.adb_handler.get_cv2_image()
    
    def capture_with_annotations(
        self,
        annotations: list,
        save_path: Optional[Union[str, Path]] = None
    ) -> str:
        """Capture screenshot with annotations.
        
        Args:
            annotations: List of annotation dictionaries
            save_path: Optional path to save screenshot
            
        Returns:
            Path to saved annotated screenshot
        """
        try:
            # Get screenshot
            image = self.capture_to_memory()
            
            # Apply annotations
            for annotation in annotations:
                self._apply_annotation(image, annotation)
            
            # Determine save path
            if save_path:
                file_path = Path(save_path)
            else:
                timestamp = int(time.time() * 1000)
                filename = f"{self.device_id}_annotated_{timestamp}.{self.format.lower()}"
                file_path = self.temp_dir / filename
            
            # Save annotated image
            cv2.imwrite(str(file_path), image)
            
            # Track file for cleanup
            if not save_path:
                self._created_files.append((str(file_path), time.time()))
            
            return str(file_path)
            
        except Exception as e:
            raise ScreenshotError(
                f"Failed to capture annotated screenshot: {e}",
                error_code="ANNOTATED_CAPTURE_FAILED"
            ) from e
    
    def _apply_annotation(self, image: np.ndarray, annotation: dict) -> None:
        """Apply single annotation to image.
        
        Args:
            image: Image to annotate
            annotation: Annotation configuration
        """
        annotation_type = annotation.get("type", "rectangle")
        color = annotation.get("color", (0, 255, 0))  # Green default
        thickness = annotation.get("thickness", 2)
        
        if annotation_type == "rectangle":
            x1, y1, x2, y2 = annotation["coords"]
            cv2.rectangle(image, (x1, y1), (x2, y2), color, thickness)
        
        elif annotation_type == "circle":
            x, y, radius = annotation["coords"]
            cv2.circle(image, (x, y), radius, color, thickness)
        
        elif annotation_type == "text":
            x, y = annotation["coords"]
            text = annotation["text"]
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = annotation.get("font_scale", 1.0)
            cv2.putText(image, text, (x, y), font, font_scale, color, thickness)
        
        elif annotation_type == "point":
            x, y = annotation["coords"]
            cv2.circle(image, (x, y), 5, color, -1)  # Filled circle
    
    def _cleanup_old_files(self) -> None:
        """Clean up old temporary files."""
        current_time = time.time()
        
        # Only cleanup periodically
        if current_time - self._last_cleanup < 300:  # 5 minutes
            return
        
        self._last_cleanup = current_time
        
        # Remove old files
        remaining_files = []
        for file_path, creation_time in self._created_files:
            if current_time - creation_time > self.cleanup_after:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        logger.debug(f"Cleaned up old screenshot: {file_path}")
                except OSError as e:
                    logger.warning(f"Failed to remove old screenshot {file_path}: {e}")
                    remaining_files.append((file_path, creation_time))
            else:
                remaining_files.append((file_path, creation_time))
        
        self._created_files = remaining_files
    
    def cleanup_all_temp_files(self) -> None:
        """Clean up all temporary files created by this handler."""
        for file_path, _ in self._created_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.debug(f"Cleaned up temp file: {file_path}")
            except OSError as e:
                logger.warning(f"Failed to remove temp file {file_path}: {e}")
        
        self._created_files.clear()
    
    def get_stats(self) -> dict:
        """Get screenshot handler statistics.
        
        Returns:
            Dictionary with statistics
        """
        return {
            "device_id": self.device_id,
            "temp_files_count": len(self._created_files),
            "temp_dir": str(self.temp_dir),
            "format": self.format,
            "quality": self.quality,
            "cleanup_after": self.cleanup_after
        }
