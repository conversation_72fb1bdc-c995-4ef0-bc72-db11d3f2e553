#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import threading
import time
from ppadb.client import Client
import sys
class KThread(threading.Thread):
 
  """A subclass of threading.Thread, with a kill()
method."""
  def __init__(self, *args, **keywords):
    threading.Thread.__init__(self, *args, **keywords)
    self.killed = False

  def start(self):
    """Start the thread."""
    self.__run_backup = self.run
    self.run = self.__run     
    threading.Thread.start(self)

  def __run(self):
    """Hacked run function, which installs the
trace."""
    sys.settrace(self.globaltrace)
    self.__run_backup()
    self.run = self.__run_backup

  def globaltrace(self, frame, why, arg):
    if why == 'call':
      return self.localtrace
    else:
      return None

  def localtrace(self, frame, why, arg):
    if self.killed:
      if why == 'line':
        raise SystemExit()
    return self.localtrace

  def kill(self):
    self.killed = True
    


class MobileClicker:
    def __init__(self, serial, x, y):
        self.adb = Client(host='localhost', port=5037)
        self.device = self.adb.device(serial)
        self.x = x
        self.y = y

    def click(self):
        self.device.shell(f'input tap {self.x} {self.y}')

    def do_clicks(self, num_clicks):
        threads = []
        for _ in range(num_clicks):
            thread = KThread(target=self.click)
            thread.start()
            threads.append(thread)
            time.sleep(0.1)

        # Wait for all threads to finish
        for thread in threads:
            thread.join()