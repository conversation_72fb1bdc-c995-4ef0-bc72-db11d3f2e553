"""Custom exceptions for Pokemon Bot."""

from typing import Optional, Any


class PokemonBotError(Exception):
    """Base exception for all Pokemon Bot errors."""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Any] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details
    
    def __str__(self) -> str:
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class ConfigurationError(PokemonBotError):
    """Raised when there's a configuration error."""
    pass


class DeviceError(PokemonBotError):
    """Raised when there's an issue with device communication."""
    pass


class ADBError(DeviceError):
    """Raised when there's an ADB-related error."""
    pass


class ImageDetectionError(PokemonBotError):
    """Raised when image detection fails."""
    pass


class OCRError(PokemonBotError):
    """Raised when OCR processing fails."""
    pass


class ScreenshotError(PokemonBotError):
    """Raised when screenshot capture fails."""
    pass


class DatabaseError(PokemonBotError):
    """Raised when database operations fail."""
    pass


class TelegramError(PokemonBotError):
    """Raised when Telegram API operations fail."""
    pass


class AuthenticationError(PokemonBotError):
    """Raised when authentication fails."""
    pass


class AdventureError(PokemonBotError):
    """Raised when adventure execution fails."""
    pass


class RetryExhaustedError(PokemonBotError):
    """Raised when retry attempts are exhausted."""
    
    def __init__(self, message: str, attempts: int, last_error: Optional[Exception] = None):
        super().__init__(message)
        self.attempts = attempts
        self.last_error = last_error


class ValidationError(PokemonBotError):
    """Raised when data validation fails."""
    pass


class TimeoutError(PokemonBotError):
    """Raised when operations timeout."""
    pass


class ResourceError(PokemonBotError):
    """Raised when resource allocation or management fails."""
    pass
