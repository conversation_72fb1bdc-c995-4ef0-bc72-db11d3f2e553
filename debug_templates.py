#!/usr/bin/env python3
"""Script de diagnóstico para entender por qué no encuentra los templates."""

import cv2
import numpy as np
from pathlib import Path
import os

def main():
    print("🔍 Diagnóstico de Templates - <PERSON><PERSON><PERSON>")
    print("=" * 50)
    
    # 1. Verificar que las imágenes existen
    assets_dir = Path("assets/images")
    print(f"\n📁 Directorio de assets: {assets_dir.absolute()}")
    print(f"   Existe: {assets_dir.exists()}")
    
    if assets_dir.exists():
        png_files = list(assets_dir.glob("*.png"))
        print(f"   Archivos PNG encontrados: {len(png_files)}")
        
        # Verificar imágenes específicas que busca el bot
        templates_to_check = [
            "menu.png",
            "adventure.png", 
            "pkbss10.png",
            "battle.png",
            "continue.png",
            "rewards.png"
        ]
        
        print(f"\n🎯 Verificando templates específicos:")
        for template in templates_to_check:
            template_path = assets_dir / template
            exists = template_path.exists()
            print(f"   {template}: {'✅' if exists else '❌'}")
            
            if exists:
                try:
                    img = cv2.imread(str(template_path))
                    if img is not None:
                        h, w = img.shape[:2]
                        print(f"      Dimensiones: {w}x{h}")
                    else:
                        print(f"      ❌ Error al cargar la imagen")
                except Exception as e:
                    print(f"      ❌ Error: {e}")
    
    # 2. Tomar screenshot actual y analizarlo
    print(f"\n📸 Analizando screenshot actual...")
    try:
        from pokemon_bot.handlers.adb_handler import ADBHandler
        
        adb = ADBHandler("192.168.1.26:5555")
        screenshot = adb.get_cv2_image()
        
        if screenshot is not None:
            h, w = screenshot.shape[:2]
            print(f"   Screenshot capturado: {w}x{h}")
            
            # Guardar screenshot para análisis
            cv2.imwrite("debug_screenshot.png", screenshot)
            print(f"   Screenshot guardado como: debug_screenshot.png")
            
            # Analizar el contenido del screenshot
            print(f"\n🔍 Análisis del screenshot:")
            
            # Convertir a escala de grises para análisis
            gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            
            # Calcular estadísticas básicas
            mean_brightness = np.mean(gray)
            std_brightness = np.std(gray)
            
            print(f"   Brillo promedio: {mean_brightness:.1f}")
            print(f"   Desviación estándar: {std_brightness:.1f}")
            
            # Detectar si es una pantalla muy oscura o muy clara
            if mean_brightness < 50:
                print(f"   ⚠️  Pantalla muy oscura - posible pantalla bloqueada")
            elif mean_brightness > 200:
                print(f"   ⚠️  Pantalla muy clara - posible pantalla en blanco")
            else:
                print(f"   ✅ Brillo normal")
            
            # Detectar bordes para ver si hay contenido
            edges = cv2.Canny(gray, 50, 150)
            edge_count = np.sum(edges > 0)
            edge_percentage = (edge_count / (w * h)) * 100
            
            print(f"   Bordes detectados: {edge_percentage:.1f}% de la imagen")
            
            if edge_percentage < 1:
                print(f"   ⚠️  Muy pocos bordes - posible pantalla uniforme")
            else:
                print(f"   ✅ Contenido detectado")
                
        else:
            print(f"   ❌ No se pudo capturar screenshot")
            
    except Exception as e:
        print(f"   ❌ Error al capturar screenshot: {e}")
    
    # 3. Probar detección con templates existentes
    print(f"\n🎯 Probando detección de templates...")
    
    if assets_dir.exists() and 'screenshot' in locals():
        # Buscar cualquier template que exista
        existing_templates = []
        for png_file in png_files[:5]:  # Solo los primeros 5 para no saturar
            try:
                template = cv2.imread(str(png_file), cv2.IMREAD_GRAYSCALE)
                if template is not None:
                    existing_templates.append((png_file.name, template))
            except:
                continue
        
        if existing_templates:
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            
            for template_name, template in existing_templates:
                try:
                    # Probar template matching
                    result = cv2.matchTemplate(screenshot_gray, template, cv2.TM_CCOEFF_NORMED)
                    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                    
                    print(f"   {template_name}: confianza máxima = {max_val:.3f}")
                    
                    if max_val > 0.8:
                        print(f"      ✅ ENCONTRADO con alta confianza!")
                    elif max_val > 0.5:
                        print(f"      🟡 Posible coincidencia")
                    else:
                        print(f"      ❌ No encontrado")
                        
                except Exception as e:
                    print(f"   {template_name}: Error - {e}")
    
    # 4. Recomendaciones
    print(f"\n💡 Recomendaciones:")
    print(f"   1. Verificar que Pokemon GO esté abierto en el dispositivo")
    print(f"   2. Asegurarse de que la pantalla no esté bloqueada")
    print(f"   3. Verificar que las imágenes template sean del juego actual")
    print(f"   4. Considerar ajustar el threshold de detección")
    print(f"   5. Revisar si las imágenes necesitan ser actualizadas")
    
    print(f"\n✅ Diagnóstico completado!")

if __name__ == "__main__":
    main()
