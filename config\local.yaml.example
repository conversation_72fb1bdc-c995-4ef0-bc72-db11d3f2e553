# Local Configuration Override Example
# Copy this file to local.yaml and customize for your environment
# Values here will override those in default.yaml

# Development settings
app:
  debug: true
  log_level: "DEBUG"

# Windows-specific OCR configuration
ocr:
  tesseract_cmd: "C:/Program Files (x86)/Tesseract-OCR/tesseract.exe"

# Local screenshot directory
screenshot:
  temp_dir: "c:/temp/"

# Your Telegram bot credentials (keep these secret!)
telegram:
  bot_token: "YOUR_BOT_TOKEN_HERE"
  chat_id: "YOUR_CHAT_ID_HERE"

# Your MongoDB connection (keep this secret!)
mongodb:
  connection_string: "mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority"

# Local device configuration
devices:
  default_device: "192.168.1.26:5555"
  
# Adventure-specific settings for testing
adventures:
  galar:
    timeout: 600  # Longer timeout for debugging
  dynamax:
    timeout: 900

# Enhanced logging for development
logging:
  level: "DEBUG"
  file:
    path: "c:/temp/pokemon_bot.log"
