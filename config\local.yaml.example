# Local Configuration - Configured with actual project credentials
# This file contains the working configuration from the original project

# Development settings
app:
  debug: true
  log_level: "DEBUG"

# Windows-specific OCR configuration
ocr:
  tesseract_cmd: "C:/Program Files (x86)/Tesseract-OCR/tesseract.exe"

# Image detection settings
image_detection:
  threshold: 0.9

# ADB configuration
adb:
  host: "localhost"
  port: 5037

# Local screenshot directory
screenshot:
  temp_dir: "c:/temp/"

# Telegram bot credentials (from original project)
telegram:
  bot_token: "**********************************************"
  chat_id: "-1001782327359"
  enabled: true

# MongoDB connection (from original project)
mongodb:
  connection_string: "mongodb+srv://pokebot:<EMAIL>/?retryWrites=true&w=majority"
  database: "pokebot"

# Local device configuration
devices:
  default_device: "************:5555"

# Adventure-specific settings for testing
adventures:
  galar:
    enabled: true
    timeout: 600  # Longer timeout for debugging
  dynamax:
    enabled: true
    timeout: 900

# Enhanced logging for development
logging:
  level: "DEBUG"
  file:
    path: "c:/temp/pokemon_bot.log"
