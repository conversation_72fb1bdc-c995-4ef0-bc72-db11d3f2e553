# -*- coding: utf-8 -*-
"""
Created on Fri Feb 21 19:10:14 2025

@author: andre
"""

import subprocess
import time
import os

class VPNManager:
    def __init__(self, container_name, search_message="Aplicando iptables NAT en tun0...", sleep_time=8, max_retries=50, lock_dir="locks"):
        """
        Gestiona un contenedor Docker asegurando que solo un proceso a la vez renueva su IP.

        :param container_name: Nombre del contenedor a reiniciar.
        :param search_message: Mensaje en los logs que indica que la IP ha sido renovada.
        :param sleep_time: Tiempo de espera tras reiniciar el contenedor.
        :param max_retries: Número máximo de intentos antes de abortar.
        :param lock_dir: Directorio donde se almacenarán los archivos de bloqueo.
        """
        self.container_name = container_name
        self.search_message = search_message
        self.sleep_time = sleep_time
        self.max_retries = max_retries
        self.lock_dir = lock_dir

        # Crear directorio de bloqueos si no existe
        if not os.path.exists(self.lock_dir):
            os.makedirs(self.lock_dir)

    def get_lock_file(self):
        """Devuelve la ruta del archivo de bloqueo para este contenedor."""
        return os.path.join(self.lock_dir, f"{self.container_name}.lock")

    def wait_for_unlock(self):
        """Espera hasta que el archivo de bloqueo del contenedor sea eliminado."""
        lock_file = self.get_lock_file()
        while os.path.exists(lock_file):
            print(f"🔒 Otro proceso está renovando la IP para {self.container_name}. Esperando...")
            time.sleep(5)

    def restart_container_until_log(self):
        """Reinicia el contenedor hasta que el mensaje deseado aparezca en los logs."""
        attempt = 0

        while attempt < self.max_retries:
            print(f"🔄 Intento {attempt + 1}/{self.max_retries}: Reiniciando contenedor {self.container_name}...")

            # Reiniciar el contenedor
            subprocess.run(["docker", "restart", self.container_name], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

            # Esperar unos segundos para que el contenedor arranque
            time.sleep(self.sleep_time)

            # Obtener los últimos 10 registros del contenedor
            logs = subprocess.run(["docker", "logs", "--tail", "10", self.container_name], capture_output=True, text=True)

            # Verificar si el mensaje aparece en los logs
            if self.search_message in logs.stdout:
                print(f"✅ Mensaje encontrado en {self.container_name}. Contenedor estable.")
                return True
            else:
                print(f"❌ Mensaje no encontrado en {self.container_name}, reintentando...")
            
            attempt += 1

        print(f"⚠️ Se alcanzó el número máximo de intentos sin éxito para {self.container_name}.")
        return False

    def run(self):
        """Ejecuta la lógica de espera y renovación de IP para este contenedor."""
        self.wait_for_unlock()  # Espera si otro proceso ya está renovando la IP

        lock_file = self.get_lock_file()

        try:
            # Crear el archivo de bloqueo para evitar que otros procesos ejecuten la función al mismo tiempo
            with open(lock_file, "w") as lock:
                lock.write("LOCKED")

            # Ejecutar la función de reinicio
            self.restart_container_until_log()

        finally:
            # Eliminar el archivo de bloqueo para que otros procesos puedan continuar
            if os.path.exists(lock_file):
                os.remove(lock_file)
            print(f"🔓 IP renovada para {self.container_name}, otros procesos pueden continuar.")
