#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import requests

class DataExtractor:
    # URL estática para la clase
    url = 'http://localhost:5001/acc2'

    @classmethod
    def fetch_data(cls):
        try:
            # Enviar una solicitud GET a la URL
            response = requests.get(cls.url)
            #print(response.text)
            # Verificar si la solicitud se completó correctamente
            if response.status_code == 200:
                # Obtener la cadena de respuesta
              
                cadena = response.text.strip('"')
                
                cadena_json = cadena.replace("ObjectId('", '"').replace("')", '"')
                cadena_json = cadena_json.replace("False", '"false"')
                # Remover las comillas simples para que sea una cadena JSON válida
                #cadena_json = cadena_json.replace("'", '"')
                cadena_json = cadena_json.replace("'", '"').replace('"\n','')
                #print(cadena_json)
                # Analizar la cadena JSON
                datos = json.loads(cadena_json)
                #print(datos)
                # Obtener el usuario y la contraseña en variables separadas
                usuario = datos.get('acc')
                contrasena = datos.get('pwd')

                return usuario, contrasena
            else:
                requests.get("http://localhost:5001/res2")
                return None, None

        except Exception as e:
            print(f'Error al realizar la solicitud HTTP: {e}')
            return None, None

# Uso de la clase

