# -*- coding: utf-8 -*-
"""
Created on Sun Dec 15 01:18:08 2024

@author: andre
"""
import cv2
import numpy as np
from config import THRESHOLD
from utils import print_current_time
class ImageDetector:
    def __init__(self):
        pass
    def detect3(self,screenshot_array, template_path, threshold=THRESHOLD):
     """Detects a template image within a screenshot using normalized correlation."""
     try:

        template = cv2.imread(template_path)
        res = cv2.matchTemplate(screenshot_array, template, cv2.TM_CCOEFF_NORMED)
        loc = np.where(res >= threshold)
        points = list(zip(*loc[::-1]))
        if points:
           print(f"Detected {template_path}")
           print_current_time(f"detected {template_path}")
           return points[0] #returns one x and y coordenate
        return None
     except Exception as e:
        print(f"Error detecting image: {e}")
        return None
    def detect4(self,screenshot_array, template_path, resize_factor=0.5, threshold=0.9):
        """Detects a template image using a more flexible scaling approach."""
        try:
            #threshold=0.5
           
            original_size = screenshot_array.shape[:2]
            if resize_factor != 1:
                screenshot_array = cv2.resize(screenshot_array, None, fx=resize_factor, fy=resize_factor, interpolation=cv2.INTER_AREA)
            
            template = cv2.imread(template_path, cv2.IMREAD_GRAYSCALE)
            if template is None:
               raise FileNotFoundError("Template not found")
            
            template = cv2.resize(template, None, fx=resize_factor, fy=resize_factor, interpolation=cv2.INTER_AREA)
            img_gray = cv2.cvtColor(screenshot_array, cv2.COLOR_BGR2GRAY)
            #best_match = None
            #scale=1
            #if scale:
            #for scale in np.linspace(1.5,0.5,10):
            #resized_template = cv2.resize(template, None, fx=scale, fy=scale, interpolation=cv2.INTER_AREA)
            res = cv2.matchTemplate(img_gray, template,cv2.TM_CCOEFF_NORMED)
              
            locations = np.where(res >= threshold)
            for loc in zip(*locations[::-1]):  # Iterar sobre las coincidencias que superan el umbral
                 match_val = res[loc[1], loc[0]]
                 if best_match is None or match_val > best_match[0]:
                   best_match = (match_val, loc, scale, resized_template.shape[1], resized_template.shape[0])
              
            if best_match:
                max_val, max_loc, scale, w, h = best_match
                top_left = (int(max_loc[0] / resize_factor), int(max_loc[1] / resize_factor))
                bottom_right = (top_left[0] + int(w / resize_factor), top_left[1] + int(h / resize_factor))
                print(scale)
                print(f"Detected, {template_path}, {bottom_right[0]}, {bottom_right[1]}")
                return bottom_right[0], bottom_right[1]
            else:
                 raise ValueError("Not found, greater the threshold.")

        except Exception as e:
            print(f"Error detec  {e}")
            return None
        
        
        
    def detect2(screenshot_array, template_path, resize_factor=0.5, threshold=0.8):
   
     try:
        # Cargar template en escala de grises
        template = cv2.imread(template_path, cv2.IMREAD_GRAYSCALE)
        if template is None:
            raise FileNotFoundError("Template not found")
            
        # Convertir screenshot a escala de grises
        screenshot_gray = cv2.cvtColor(screenshot_array, cv2.COLOR_BGR2GRAY)
        
        best_match = None
        best_val = -1
        
        # Multi-scale detection para mayor precisión
        scales = [0.8, 0.9, 1.0, 1.1, 1.2]
        
        for scale in scales:
            # Redimensionar template
            current_template = cv2.resize(template, None, 
                                       fx=scale * resize_factor, 
                                       fy=scale * resize_factor, 
                                       interpolation=cv2.INTER_LINEAR)
            
            # Redimensionar screenshot
            current_screenshot = cv2.resize(screenshot_gray, None,
                                         fx=resize_factor, 
                                         fy=resize_factor,
                                         interpolation=cv2.INTER_LINEAR)
            
            # Template matching usando TM_CCORR_NORMED (más rápido)
            result = cv2.matchTemplate(current_screenshot, 
                                     current_template, 
                                     cv2.TM_CCORR_NORMED)
            
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val > best_val:
                best_val = max_val
                w = int(current_template.shape[1] / resize_factor)
                h = int(current_template.shape[0] / resize_factor)
                x = int(max_loc[0] / resize_factor)
                y = int(max_loc[1] / resize_factor)
                best_match = (x, y, w, h, max_val)
        
        if best_match and best_val >= threshold:
            x, y, w, h, conf = best_match
            bottom_right = (x + w, y + h)
            print(f"Detected {template_path}, {bottom_right[0]}, {bottom_right[1]}, confidence: {conf:.2f}")
            return bottom_right[0], bottom_right[1]
        else:
            raise ValueError(f"Not found, best confidence {best_val:.2f} below threshold {threshold}")

     except Exception as e:
        print(f"Error detect: {e}")
        return None
    
    def detect(self, screenshot_array, template_path, resize_factor=0.5, threshold=0.9):
     """Detects a template image using a more flexible scaling approach."""
     try:
        #threshold=0.5

        original_size = screenshot_array.shape[:2]
        if resize_factor != 1:
            screenshot_array = cv2.resize(screenshot_array, None, fx=resize_factor, fy=resize_factor, interpolation=cv2.INTER_AREA)

        template = cv2.imread(template_path, cv2.IMREAD_GRAYSCALE)
        if template is None:
           raise FileNotFoundError("Template not found")

        # Guardar dimensiones originales del template antes de redimensionar
        original_template_size = template.shape[:2]

        template = cv2.resize(template, None, fx=resize_factor, fy=resize_factor, interpolation=cv2.INTER_AREA)

        # Validar que la plantilla no sea más grande que el screenshot
        if template.shape[0] > screenshot_array.shape[0] or template.shape[1] > screenshot_array.shape[1]:
            raise ValueError(f"Template size ({template.shape[1]}x{template.shape[0]}) is larger than screenshot ({screenshot_array.shape[1]}x{screenshot_array.shape[0]}) after resizing")

        img_gray = cv2.cvtColor(screenshot_array, cv2.COLOR_BGR2GRAY)

        # Inicializar best_match antes de usarlo
        best_match = None
        scale = 1  # Definir scale ya que se usa más adelante

        res = cv2.matchTemplate(img_gray, template, cv2.TM_CCOEFF_NORMED)

        locations = np.where(res >= threshold)
        for loc in zip(*locations[::-1]):  # Iterar sobre las coincidencias que superan el umbral
             match_val = res[loc[1], loc[0]]
             if best_match is None or match_val > best_match[0]:
               best_match = (match_val, loc, scale, template.shape[1], template.shape[0])  # Usar template en lugar de resized_template

        if best_match:
            max_val, max_loc, scale, w, h = best_match

            # Convertir coordenadas a la escala original
            top_left = (int(max_loc[0] / resize_factor), int(max_loc[1] / resize_factor))

            # Usar las dimensiones originales del template para calcular bottom_right
            bottom_right = (top_left[0] + int(original_template_size[1]), top_left[1] + int(original_template_size[0]))

            print(scale)
            print(f"Detected, {template_path}, {bottom_right[0]}, {bottom_right[1]}")
            return bottom_right[0], bottom_right[1]
        else:
             #return False
             raise ValueError("Not found, greater the threshold.")

     except Exception as e:
        print(f"Error detec  {e}")
        return None