"""Retry mechanisms and error handling utilities."""

import time
import random
from typing import Callable, Type, Union, Tuple, Optional, Any
from functools import wraps
import asyncio

from .exceptions import RetryExhaustedError, PokemonBotError
from .logging import get_logger

logger = get_logger(__name__)


class RetryConfig:
    """Configuration for retry behavior."""
    
    def __init__(
        self,
        max_attempts: int = 3,
        backoff_factor: float = 2.0,
        max_delay: float = 60.0,
        jitter: bool = True,
        exceptions: Tuple[Type[Exception], ...] = (Exception,)
    ):
        self.max_attempts = max_attempts
        self.backoff_factor = backoff_factor
        self.max_delay = max_delay
        self.jitter = jitter
        self.exceptions = exceptions
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate delay for the given attempt."""
        delay = min(self.backoff_factor ** attempt, self.max_delay)
        
        if self.jitter:
            # Add jitter to prevent thundering herd
            delay *= (0.5 + random.random() * 0.5)
        
        return delay


def retry(
    max_attempts: int = 3,
    backoff_factor: float = 2.0,
    max_delay: float = 60.0,
    exceptions: Union[Type[Exception], Tuple[Type[Exception], ...]] = Exception,
    jitter: bool = True,
    on_retry: Optional[Callable[[int, Exception], None]] = None
):
    """Decorator for retrying function calls with exponential backoff.
    
    Args:
        max_attempts: Maximum number of retry attempts
        backoff_factor: Multiplier for delay between retries
        max_delay: Maximum delay between retries
        exceptions: Exception types to retry on
        jitter: Whether to add random jitter to delays
        on_retry: Callback function called on each retry
    """
    if isinstance(exceptions, type):
        exceptions = (exceptions,)
    
    config = RetryConfig(max_attempts, backoff_factor, max_delay, jitter, exceptions)
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(config.max_attempts):
                try:
                    return func(*args, **kwargs)
                except config.exceptions as e:
                    last_exception = e
                    
                    if attempt == config.max_attempts - 1:
                        # Last attempt failed
                        break
                    
                    delay = config.calculate_delay(attempt)
                    
                    logger.warning(
                        f"Attempt {attempt + 1}/{config.max_attempts} failed for {func.__name__}: {e}. "
                        f"Retrying in {delay:.2f}s"
                    )
                    
                    if on_retry:
                        on_retry(attempt + 1, e)
                    
                    time.sleep(delay)
            
            # All attempts failed
            raise RetryExhaustedError(
                f"Function {func.__name__} failed after {config.max_attempts} attempts",
                config.max_attempts,
                last_exception
            )
        
        return wrapper
    return decorator


def async_retry(
    max_attempts: int = 3,
    backoff_factor: float = 2.0,
    max_delay: float = 60.0,
    exceptions: Union[Type[Exception], Tuple[Type[Exception], ...]] = Exception,
    jitter: bool = True,
    on_retry: Optional[Callable[[int, Exception], None]] = None
):
    """Async version of retry decorator."""
    if isinstance(exceptions, type):
        exceptions = (exceptions,)
    
    config = RetryConfig(max_attempts, backoff_factor, max_delay, jitter, exceptions)
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(config.max_attempts):
                try:
                    return await func(*args, **kwargs)
                except config.exceptions as e:
                    last_exception = e
                    
                    if attempt == config.max_attempts - 1:
                        break
                    
                    delay = config.calculate_delay(attempt)
                    
                    logger.warning(
                        f"Attempt {attempt + 1}/{config.max_attempts} failed for {func.__name__}: {e}. "
                        f"Retrying in {delay:.2f}s"
                    )
                    
                    if on_retry:
                        on_retry(attempt + 1, e)
                    
                    await asyncio.sleep(delay)
            
            raise RetryExhaustedError(
                f"Function {func.__name__} failed after {config.max_attempts} attempts",
                config.max_attempts,
                last_exception
            )
        
        return wrapper
    return decorator


class CircuitBreaker:
    """Circuit breaker pattern implementation."""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        expected_exception: Type[Exception] = Exception
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def __call__(self, func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            if self.state == "OPEN":
                if self._should_attempt_reset():
                    self.state = "HALF_OPEN"
                else:
                    raise PokemonBotError(
                        f"Circuit breaker is OPEN for {func.__name__}",
                        error_code="CIRCUIT_BREAKER_OPEN"
                    )
            
            try:
                result = func(*args, **kwargs)
                self._on_success()
                return result
            except self.expected_exception as e:
                self._on_failure()
                raise e
        
        return wrapper
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        return (
            self.last_failure_time is not None and
            time.time() - self.last_failure_time >= self.recovery_timeout
        )
    
    def _on_success(self) -> None:
        """Handle successful call."""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def _on_failure(self) -> None:
        """Handle failed call."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"
            logger.warning(f"Circuit breaker opened after {self.failure_count} failures")


def safe_execute(
    func: Callable,
    *args,
    default_return: Any = None,
    log_errors: bool = True,
    **kwargs
) -> Any:
    """Safely execute a function with error handling.
    
    Args:
        func: Function to execute
        *args: Function arguments
        default_return: Value to return on error
        log_errors: Whether to log errors
        **kwargs: Function keyword arguments
    
    Returns:
        Function result or default_return on error
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        if log_errors:
            logger.error(f"Error executing {func.__name__}: {e}", exc_info=True)
        return default_return


class ErrorHandler:
    """Centralized error handling."""
    
    def __init__(self):
        self.error_counts = {}
        self.error_handlers = {}
    
    def register_handler(self, exception_type: Type[Exception], handler: Callable[[Exception], None]) -> None:
        """Register an error handler for a specific exception type."""
        self.error_handlers[exception_type] = handler
    
    def handle_error(self, error: Exception, context: Optional[str] = None) -> None:
        """Handle an error with registered handlers."""
        error_type = type(error)
        
        # Update error count
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # Log error
        logger.error(
            f"Error handled{f' in {context}' if context else ''}: {error}",
            exc_info=True,
            error_type=error_type.__name__,
            error_count=self.error_counts[error_type]
        )
        
        # Call registered handler
        if error_type in self.error_handlers:
            try:
                self.error_handlers[error_type](error)
            except Exception as handler_error:
                logger.error(f"Error in error handler: {handler_error}", exc_info=True)
    
    def get_error_stats(self) -> dict:
        """Get error statistics."""
        return self.error_counts.copy()


# Global error handler instance
_error_handler = ErrorHandler()


def get_error_handler() -> ErrorHandler:
    """Get the global error handler."""
    return _error_handler
