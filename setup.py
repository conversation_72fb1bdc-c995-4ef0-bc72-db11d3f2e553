#!/usr/bin/env python3
"""Setup script for Pokemon Bot."""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="pokemon-bot",
    version="2.0.0",
    author="Pokemon Bot Team",
    author_email="<EMAIL>",
    description="Advanced Pokemon GO automation bot with multi-device support",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/pokemon-bot/pokemon-bot",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-cov>=4.1.0",
            "pytest-mock>=3.11.0",
            "black>=23.7.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
            "pre-commit>=3.3.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "pokemon-bot=pokemon_bot.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "pokemon_bot": ["assets/images/*.png"],
    },
)
