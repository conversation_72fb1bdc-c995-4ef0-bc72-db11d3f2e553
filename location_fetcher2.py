# -*- coding: utf-8 -*-
"""
Created on Sun Jun 22 05:32:22 2025

@author: andre
"""

import datetime

class LocationFetcher:
    @staticmethod
    def get_primary_location():
        hora_actual = datetime.datetime.now().hour
        
        url = ""
        target_lat = 0
        target_lon = 0

        if 18 <= hora_actual <= 23:

            url = "https://moonani.com/PokeList/Pier39/raids.php?"
            target_lat = 37.8086
            target_lon = -122.4098          

            url = "https://moonani.com/PokeList/Pier39/raids.php?"
            target_lat = 37.8086
            target_lon = -122.4098  
            url = "https://moonani.com/PokeList/Honolulu/raids.php?"
            target_lat = 21.2710
            target_lon = -157.8215
            url = "https://moonani.com/PokeList/Pier39/raids.php?"
            target_lat = 37.8086
            target_lon = -122.4098  
            url = "https://moonani.com/PokeList/Honolulu/raids.php?"
            target_lat = 21.2710
            target_lon = -157.8215            
              
            url = "https://moonani.com/PokeList/Honolulu/raids.php?"
            target_lat = 21.2710
            target_lon = -157.8215                
            url = "https://moonani.com/PokeList/Pier39/raids.php?"
            target_lat = 37.8086
            target_lon = -122.4098       
            url= "https://sydneypogomap.com/raids.php?"
            target_lat = -33.8614
            target_lon = 151.2120 
            



                             
        elif 0 <= hora_actual <= 5:
            url= "https://sydneypogomap.com/raids.php?"
            target_lat = -33.860622
            target_lon = 151.2067351
            url = "https://moonani.com/PokeList/Zara/raids.php?"
            target_lat = 41.6612
            target_lon = -0.8918    
            url="https://moonani.com/PokeList/Osaka/raids.php"
            target_lat = 35.9704
            target_lon = -78.5208
            url = "https://sgpokemap.com/raids.php?"
            target_lat = 1.3745
            target_lon = 103.8374            

            url = "https://moonani.com/PokeList/Zara/raids.php?"
            target_lat = 41.6612
            target_lon = -0.8918       
            url= "https://sydneypogomap.com/raids.php?"
            target_lat = -33.8614
            target_lon = 151.2120
            url = "https://sgpokemap.com/raids.php?"
            target_lat = 1.3745
            target_lon = 103.8374 
            url = "https://nycpokemap.com/raids.php?"
            target_lat = 40.7552
            target_lon = -73.9830            

            
        elif 0 == hora_actual :
          url = "https://moonani.com/PokeList/Honolulu/raids.php?"
          target_lat = 21.293342
          target_lon = -157.84622
          url = "https://moonani.com/PokeList/Zara/raids.php?"
          target_lat = 41.6604
          target_lon = -0.8866   
          url= "https://sydneypogomap.com/raids.php?"
          target_lat = -33.8614
          target_lon = 151.2120 

        elif 6 <= hora_actual <=18:
     
            url = "https://moonani.com/PokeList/Pier39/raids.php?"
            target_lat = 37.8086
            target_lon = -122.4098  
            url= "https://sydneypogomap.com/raids.php?"
            target_lat = -33.8614
            target_lon = 151.2120 



        else:
            url = "https://moonani.com/PokeList/Honolulu/raids.php?"
            target_lat = 21.293342
            target_lon = -157.84622
            url="https://moonani.com/PokeList/Osaka/raids.php"
            target_lat = 35.9704
            target_lon = -78.5208
            url = "https://moonani.com/PokeList/Honolulu/raids.php?"
            target_lat = 21.2710
            target_lon = -157.8215        
            url= "https://sydneypogomap.com/raids.php?"
            target_lat = -33.8614
            target_lon = 151.2120 

        return url, target_lat, target_lon

    @staticmethod
    def get_secondary_location():
        hora_actual = datetime.datetime.now().hour
        
        url = ""
        target_lat = 0
        target_lon = 0

        if 20 <= hora_actual <= 23:
            url = "https://sgpokemap.com/raids.php?"
            target_lat = 1.3745
            target_lon = 103.8374
            url = "https://moonani.com/PokeList/Honolulu/raids.php?"
            target_lat = 21.2710
            target_lon = -157.8215  
            
        elif 0 <= hora_actual <= 12:
            url = "https://londonpogomap.com/raids.php?"
            target_lat = -33.860622
            target_lon = -0.117231
            
        elif 13 <= hora_actual <= 20:
            url= "https://sydneypogomap.com/raids.php?"
            target_lat = -33.860622
            target_lon = 151.2067351
        else:
            url="https://moonani.com/PokeList/Osaka/raids.php"
            target_lat = 35.9704
            target_lon = -78.5208
            url = "https://moonani.com/PokeList/Honolulu/raids.php?"
            target_lat = 21.2710
            target_lon = -157.8215  
 
        return url, target_lat, target_lon