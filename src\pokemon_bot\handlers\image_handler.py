"""Enhanced image detection handler with caching and optimization."""

import cv2
import numpy as np
from pathlib import Path
from typing import Optional, Tuple, Dict, List, Union
from functools import lru_cache
import time

from ..core.config import get_config
from ..core.exceptions import ImageDetectionError
from ..core.logging import get_logger
from ..core.retry import retry

logger = get_logger(__name__)


class ImageCache:
    """LRU cache for template images."""
    
    def __init__(self, max_size: int = 100):
        self.max_size = max_size
        self._cache: Dict[str, np.ndarray] = {}
        self._access_times: Dict[str, float] = {}
    
    def get(self, path: str) -> Optional[np.ndarray]:
        """Get cached template image."""
        if path in self._cache:
            self._access_times[path] = time.time()
            return self._cache[path]
        return None
    
    def put(self, path: str, image: np.ndarray) -> None:
        """Cache template image."""
        if len(self._cache) >= self.max_size:
            self._evict_oldest()
        
        self._cache[path] = image.copy()
        self._access_times[path] = time.time()
    
    def _evict_oldest(self) -> None:
        """Evict least recently used item."""
        if not self._access_times:
            return
        
        oldest_path = min(self._access_times.keys(), key=lambda k: self._access_times[k])
        del self._cache[oldest_path]
        del self._access_times[oldest_path]
    
    def clear(self) -> None:
        """Clear cache."""
        self._cache.clear()
        self._access_times.clear()


class ImageHandler:
    """Enhanced image detection with caching and multiple algorithms."""
    
    def __init__(self, assets_dir: Optional[Union[str, Path]] = None):
        """Initialize image handler.
        
        Args:
            assets_dir: Directory containing template images
        """
        self.config = get_config()
        self.assets_dir = Path(assets_dir) if assets_dir else self._get_default_assets_dir()
        
        # Initialize cache if enabled
        if self.config.image_detection.cache_enabled:
            self.cache = ImageCache(self.config.image_detection.cache_size)
        else:
            self.cache = None
        
        # Template matching method
        self.matching_methods = {
            "TM_CCOEFF_NORMED": cv2.TM_CCOEFF_NORMED,
            "TM_CCORR_NORMED": cv2.TM_CCORR_NORMED,
            "TM_SQDIFF_NORMED": cv2.TM_SQDIFF_NORMED
        }
        
        self.method = self.matching_methods.get(
            self.config.image_detection.template_matching_method,
            cv2.TM_CCOEFF_NORMED
        )
    
    def _get_default_assets_dir(self) -> Path:
        """Get default assets directory."""
        # Try to find assets directory relative to the package
        current_dir = Path(__file__).parent.parent.parent.parent
        assets_dir = current_dir / "assets" / "images"
        
        if not assets_dir.exists():
            # Fallback to current working directory
            assets_dir = Path.cwd() / "assets" / "images"
        
        return assets_dir
    
    def _load_template(self, template_name: str) -> np.ndarray:
        """Load template image with caching.
        
        Args:
            template_name: Name of template image file
            
        Returns:
            Template image as numpy array
            
        Raises:
            ImageDetectionError: If template cannot be loaded
        """
        template_path = self.assets_dir / template_name
        template_path_str = str(template_path)
        
        # Check cache first
        if self.cache:
            cached_template = self.cache.get(template_path_str)
            if cached_template is not None:
                return cached_template
        
        # Load template
        if not template_path.exists():
            raise ImageDetectionError(
                f"Template image not found: {template_path}",
                error_code="TEMPLATE_NOT_FOUND"
            )
        
        try:
            template = cv2.imread(str(template_path), cv2.IMREAD_GRAYSCALE)
            if template is None:
                raise ImageDetectionError(
                    f"Failed to load template image: {template_path}",
                    error_code="TEMPLATE_LOAD_FAILED"
                )
            
            # Cache the template
            if self.cache:
                self.cache.put(template_path_str, template)
            
            return template
            
        except Exception as e:
            raise ImageDetectionError(
                f"Error loading template {template_name}: {e}",
                error_code="TEMPLATE_LOAD_ERROR"
            ) from e
    
    def _preprocess_image(self, image: np.ndarray, resize_factor: Optional[float] = None) -> np.ndarray:
        """Preprocess image for detection.
        
        Args:
            image: Input image
            resize_factor: Resize factor (None to use config default)
            
        Returns:
            Preprocessed image
        """
        if resize_factor is None:
            resize_factor = self.config.image_detection.resize_factor
        
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Resize if needed
        if resize_factor != 1.0:
            height, width = image.shape[:2]
            new_width = int(width * resize_factor)
            new_height = int(height * resize_factor)
            image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
        
        return image
    
    @retry(max_attempts=2, exceptions=ImageDetectionError)
    def detect(
        self,
        screenshot: np.ndarray,
        template_name: str,
        threshold: Optional[float] = None,
        resize_factor: Optional[float] = None,
        region: Optional[Tuple[int, int, int, int]] = None
    ) -> Optional[Tuple[int, int]]:
        """Detect template in screenshot.
        
        Args:
            screenshot: Screenshot image
            template_name: Name of template image file
            threshold: Detection threshold (None to use config default)
            resize_factor: Resize factor (None to use config default)
            region: Region to search in (x1, y1, x2, y2)
            
        Returns:
            Center coordinates of detected template, or None if not found
        """
        if threshold is None:
            threshold = self.config.image_detection.threshold
        
        try:
            # Crop to region if specified
            if region:
                x1, y1, x2, y2 = region
                search_area = screenshot[y1:y2, x1:x2]
                offset_x, offset_y = x1, y1
            else:
                search_area = screenshot
                offset_x, offset_y = 0, 0
            
            # Preprocess images
            processed_screenshot = self._preprocess_image(search_area, resize_factor)
            template = self._load_template(template_name)
            
            # Resize template to match screenshot scale
            if resize_factor and resize_factor != 1.0:
                template_height, template_width = template.shape[:2]
                new_template_width = int(template_width * resize_factor)
                new_template_height = int(template_height * resize_factor)
                template = cv2.resize(template, (new_template_width, new_template_height))

            # Verificar que el template no sea más grande que la imagen de búsqueda
            template_height, template_width = template.shape[:2]
            search_height, search_width = processed_screenshot.shape[:2]

            if template_height > search_height or template_width > search_width:
                # Escalar el template para que quepa en la imagen de búsqueda
                scale_h = search_height / template_height if template_height > search_height else 1.0
                scale_w = search_width / template_width if template_width > search_width else 1.0
                scale = min(scale_h, scale_w, 0.9)  # Máximo 90% del tamaño de búsqueda

                new_width = int(template_width * scale)
                new_height = int(template_height * scale)
                template = cv2.resize(template, (new_width, new_height))

                logger.debug(f"Template {template_name} redimensionado de {template_width}x{template_height} a {new_width}x{new_height}")
            
            # Perform template matching
            result = cv2.matchTemplate(processed_screenshot, template, self.method)
            
            # Find best match
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            # Determine if match is good enough
            if self.method == cv2.TM_SQDIFF_NORMED:
                match_val = min_val
                match_loc = min_loc
                is_good_match = match_val < (1.0 - threshold)
            else:
                match_val = max_val
                match_loc = max_loc
                is_good_match = match_val >= threshold
            
            if is_good_match:
                # Calculate center coordinates
                template_height, template_width = template.shape[:2]
                center_x = match_loc[0] + template_width // 2
                center_y = match_loc[1] + template_height // 2
                
                # Adjust for resize factor and region offset
                if resize_factor and resize_factor != 1.0:
                    center_x = int(center_x / resize_factor)
                    center_y = int(center_y / resize_factor)
                
                center_x += offset_x
                center_y += offset_y
                
                logger.log_image_detection(
                    template_name, True, "unknown",
                    confidence=match_val, coordinates=(center_x, center_y)
                )
                
                return (center_x, center_y)
            else:
                logger.log_image_detection(
                    template_name, False, "unknown",
                    confidence=match_val, threshold=threshold
                )
                return None
                
        except ImageDetectionError:
            raise
        except Exception as e:
            raise ImageDetectionError(
                f"Error detecting template {template_name}: {e}",
                error_code="DETECTION_ERROR"
            ) from e
    
    def detect_multiple(
        self,
        screenshot: np.ndarray,
        template_names: List[str],
        threshold: Optional[float] = None,
        resize_factor: Optional[float] = None
    ) -> Dict[str, Optional[Tuple[int, int]]]:
        """Detect multiple templates in screenshot.
        
        Args:
            screenshot: Screenshot image
            template_names: List of template image file names
            threshold: Detection threshold
            resize_factor: Resize factor
            
        Returns:
            Dictionary mapping template names to coordinates (or None if not found)
        """
        results = {}
        for template_name in template_names:
            try:
                result = self.detect(screenshot, template_name, threshold, resize_factor)
                results[template_name] = result
            except Exception as e:
                logger.error(f"Error detecting {template_name}: {e}")
                results[template_name] = None
        
        return results
    
    def wait_for_image(
        self,
        screenshot_func: callable,
        template_name: str,
        timeout: int = 30,
        interval: float = 1.0,
        threshold: Optional[float] = None
    ) -> Optional[Tuple[int, int]]:
        """Wait for image to appear on screen.
        
        Args:
            screenshot_func: Function that returns current screenshot
            template_name: Template image name
            timeout: Maximum wait time in seconds
            interval: Check interval in seconds
            threshold: Detection threshold
            
        Returns:
            Coordinates if found, None if timeout
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                screenshot = screenshot_func()
                result = self.detect(screenshot, template_name, threshold)
                if result:
                    return result
            except Exception as e:
                logger.debug(f"Error during image wait: {e}")
            
            time.sleep(interval)
        
        return None
    
    def clear_cache(self) -> None:
        """Clear template cache."""
        if self.cache:
            self.cache.clear()
    
    def get_cache_stats(self) -> dict:
        """Get cache statistics."""
        if not self.cache:
            return {"enabled": False}
        
        return {
            "enabled": True,
            "size": len(self.cache._cache),
            "max_size": self.cache.max_size,
            "hit_rate": "N/A"  # Could implement hit rate tracking
        }
