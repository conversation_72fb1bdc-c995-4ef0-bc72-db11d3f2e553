#!/usr/bin/env python3
"""Test script for restore functionality."""

import time
import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from pokemon_bot.handlers.adb_handler import ADBHandler
from pokemon_bot.services.auth_service import AuthService

class MockBot:
    """Mock bot for testing restore functionality."""
    
    def __init__(self, adb):
        self.adb = adb
    
    def detect_image(self, image_name):
        """Mock detect image function."""
        print(f"Mock: Detecting image {image_name}")
        return False  # Always return False for testing
    
    def restart_pokemon_go(self):
        """Mock restart Pokemon GO function."""
        print("Mock: Restarting Pokemon GO")
        self.adb.shell('am force-stop com.nianticlabs.pokemongo')
        time.sleep(2)
        self.adb.shell('am start -n com.nianticlabs.pokemongo/com.nianticproject.holoholo.libholoholo.unity.UnityPlayerActivity')

def test_restore():
    """Test the restore functionality."""
    print("🔍 Testing restore functionality...")
    
    # Initialize ADB
    device_id = "192.168.1.26:5555"
    adb = ADBHandler(device_id)
    
    if not adb.is_connected():
        print("❌ Failed to connect to ADB device")
        return False
    
    print(f"✅ Connected to device: {device_id}")
    
    # Create mock bot
    mock_bot = MockBot(adb)
    
    # Create auth service
    auth_service = AuthService(mock_bot)
    
    # Test restore for a specific user
    test_user = "AndresAf1001"
    test_password = "test123"
    
    print(f"🔄 Testing restore for user: {test_user}")
    
    try:
        # Test the restore function
        result = auth_service._restore_user_preferences(test_user, test_password)
        
        if result:
            print("✅ Restore completed successfully!")
        else:
            print("❌ Restore failed!")
        
        return result
        
    except Exception as e:
        print(f"❌ Error during restore: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_backup_files():
    """Check if backup files exist."""
    print("\n📁 Checking backup files...")
    
    users_to_check = ["AndresAf1001", "AndresAf1002", "retrt"]
    
    for user in users_to_check:
        backup_path = os.path.join("c:/temp/", user)
        exists = os.path.exists(backup_path)
        print(f"  {user}: {'✅ EXISTS' if exists else '❌ NOT FOUND'} - {backup_path}")
    
    return True

def test_chrome_cleanup():
    """Test Chrome cleanup functionality."""
    print("\n🧹 Testing Chrome cleanup...")
    
    device_id = "192.168.1.26:5555"
    adb = ADBHandler(device_id)
    
    if not adb.is_connected():
        print("❌ Failed to connect to ADB device")
        return False
    
    mock_bot = MockBot(adb)
    auth_service = AuthService(mock_bot)
    
    try:
        auth_service._clean_chrome_data()
        print("✅ Chrome cleanup completed!")
        return True
    except Exception as e:
        print(f"❌ Chrome cleanup failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Pokemon Bot Restore Test")
    print("=" * 50)
    
    # Check backup files first
    check_backup_files()
    
    # Test Chrome cleanup
    test_chrome_cleanup()
    
    # Test full restore
    success = test_restore()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed!")
    else:
        print("💥 Tests failed!")
