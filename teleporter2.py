# -*- coding: utf-8 -*-
"""
Created on Sun Jun 22 05:05:07 2025

@author: andre
"""

# -*- coding: utf-8 -*-
"""
Created on Thu Mar  6 03:54:24 2025

@author: andre
"""
import sys
import requests
import time
from ppadb.client import Client
from location_fetcher2 import LocationFetcher


class Teleporter:
    def __init__(self, device_id, pokedex_number):
        self.adb = Client(host='localhost', port=5037)
        self.device = self.adb.device(device_id)
        self.pokedex_number = str(pokedex_number)  # Convertimos a string por compatibilidad

    def get_coor(self, url):
        """ Obtiene las coordenadas de incursiones del Pokémon especificado """
        response = requests.get(url)
        coordinate_list = []

        if response.status_code == 200:
            data = response.json()
            #print(data)
            for raid in data.get("raids", []):
                level = raid.get("level")
                #print(level)
                latitude = raid.get("lat")
                longitude = raid.get("lng")
                #numero = str(raid.get("pokemon_id"))

                #if numero == self.pokedex_number and time.time() > raid.get("raid_start"):
                coordinate_list.append((latitude, longitude))
        #print(coordinate_list)
        return coordinate_list

    def find_nearest_coordinate(self, target_lat, target_lon, coordinates):
        """ Encuentra la coordenada más cercana al objetivo """
        nearest_coordinate = None
        min_distance = float("inf")

        for lat, lon in coordinates:
            if  lat != target_lat and lon != target_lon:
               
                    
                    distance = ((lat - target_lat) ** 2 + (lon - target_lon) ** 2) ** 0.5
        
                    if distance < min_distance:
                        min_distance = distance
                        nearest_coordinate = (lat, lon)

        return nearest_coordinate
        #,coordinates[-1]

    def teleport_to_location(self, lat, lon):
        """ Teletransporta el dispositivo a la ubicación dada usando ADB """
        self.device.shell(f'am start -W -a android.intent.action.VIEW -d https://fly.mg/coords?ll={lat},{lon}')
        print(f"Teletransportando a {lat}, {lon} (Pokédex ID: {self.pokedex_number})")
        print(f'am start -W -a android.intent.action.VIEW -d https://fly.mg/coords?ll={lat},{lon}')
 
    def teleport_pokemon(self):
        """ Lógica principal para encontrar incursiones y teletransportar """
        print(f"Buscando incursiones de Pokédex ID: {self.pokedex_number}...")

        url_primaria, target_lat, target_lon = LocationFetcher.get_primary_location()
        
        import datetime
        fecha_hora_actual = datetime.datetime.now()
        timestamp_actual = str(fecha_hora_actual.timestamp() * 1000).replace(".", "")

        coordinates_list = self.get_coor(url_primaria + "time=" + timestamp_actual)

        if not coordinates_list:
            print("No se encontraron incursiones en la ubicación primaria. Probando ubicación secundaria...")
            url_sec, target_lat, target_lon = LocationFetcher.get_secondary_location()
            coordinates_list = self.get_coor(url_sec)

        if coordinates_list:
            nearest_coordinate = self.find_nearest_coordinate(target_lat, target_lon, coordinates_list)
            return nearest_coordinate
            #self.teleport_to_location(nearest_coordinate[0], nearest_coordinate[1])
        else:
            print(f"No se encontraron incursiones activas de Pokédex ID: {self.pokedex_number}.")

    def nearest_poke(self, lat, lon):
        """ Lógica principal para encontrar incursiones y teletransportar """
        print(f"Buscando incursiones de Pokédex ID: {self.pokedex_number}...")

        url_primaria, target_lat, target_lon = LocationFetcher.get_primary_location()
        
        import datetime
        fecha_hora_actual = datetime.datetime.now()
        timestamp_actual = str(fecha_hora_actual.timestamp() * 1000).replace(".", "")

        coordinates_list = self.get_coor(url_primaria + "time=" + timestamp_actual)

        if not coordinates_list:
            print("No se encontraron incursiones en la ubicación primaria. Probando ubicación secundaria...")
            url_sec, target_lat, target_lon = LocationFetcher.get_secondary_location()
            coordinates_list = self.get_coor(url_sec)

        if coordinates_list:
            nearest_coordinate = self.find_nearest_coordinate(lat, lon, coordinates_list)
            return nearest_coordinate
            #self.teleport_to_location(nearest_coordinate[0], nearest_coordinate[1])
        else:
            print(f"No se encontraron incursiones activas de Pokédex ID: {self.pokedex_number}.")




if __name__ == "__main__":
    id_ = "R28M20D0KGW"
    pokedex_id = 6
    teleporter = Teleporter(id_, pokedex_id)

    print(f"Buscando todas las coordenadas de incursiones de Pokédex ID: {pokedex_id}")
    
    # Obtener coordenadas desde la URL primaria
    url, _, _ = LocationFetcher.get_primary_location()
    
    import datetime
    timestamp = str(datetime.datetime.now().timestamp() * 1000).replace(".", "")
    coordinates_list = teleporter.get_coor(url + "time=" + timestamp)

    # Si no hay resultados, intentar secundaria
    if not coordinates_list:
        print("Probando ubicación secundaria...")
        url, _, _ = LocationFetcher.get_secondary_location()
        coordinates_list = teleporter.get_coor(url)

    if coordinates_list:
        print(f"Se encontraron {len(coordinates_list)} coordenadas:\n")
        for idx, (lat, lon) in enumerate(coordinates_list, start=1):
            print(f"{idx:02d}. Lat: {lat:.6f}, Lon: {lon:.6f}")
    else:
        print("No se encontraron incursiones activas.")



        