import time
import requests
import subprocess
import os

class CambiarIP:
    lock_file_path = "cambiar_ip3.lock"  # Archivo de bloqueo compartido

    def __init__(self):
        if os.path.exists(CambiarIP.lock_file_path):
            print("Otro proceso ya está ejecutando el cambio de IP. Esperando...")
            self._esperar_cambio_ip()
        else:
            self._crear_archivo_bloqueo()
            self.ip_inicial = self._obtener_ip_inicial()

            if not self.ip_inicial:
                print("No se pudo obtener la IP inicial. Terminando...")
                self._eliminar_archivo_bloqueo()
                return

            # Intentar cambiar la IP con reintento automático
            self._cambiar_ip_con_reintento()

            self._eliminar_archivo_bloqueo()

    def _crear_archivo_bloqueo(self):
        with open(CambiarIP.lock_file_path, "w") as lock_file:
            lock_file.write("Bloqueo activo: otro proceso está cambiando la IP.\n")
        print("Archivo de bloqueo creado.")

    def _obtener_ip_inicial(self):
        servicios = [
            "https://api.ipify.org",
            "https://checkip.amazonaws.com",
            "http://ifconfig.me",
        ]
        for servicio in servicios:
            for intento in range(3):  # Reintentar hasta 3 veces por servicio
                try:
                    print(f"Intento {intento + 1}: Obteniendo la IP desde {servicio}...")
                    ip = requests.get(servicio, timeout=10).text.strip()
                    print(f"IP inicial obtenida desde {servicio}: {ip}")
                    return ip
                except requests.RequestException as e:
                    print(f"Error al obtener la IP desde {servicio} en el intento {intento + 1}: {e}")
                    time.sleep(5)  # Esperar 5 segundos antes de reintentar
            print(f"No se pudo obtener la IP desde {servicio}. Probando con otro servicio...")
        print("No se pudo obtener la IP inicial desde ningún servicio.")
        return None

    def _cambiar_ip_con_reintento(self):
        """
        Cambia la IP y reinicia el proceso si no se logra el cambio en 2 minutos.
        """
        while True:
            if self._cambiar_ip_y_verificar():
                print("IP cambiada exitosamente. Terminando proceso...")
                break
            else:
                print("La IP no cambió dentro del tiempo asignado. Reiniciando NordVPN y reintentando...")
                self._reiniciar_nordvpn()

    def _cambiar_ip_y_verificar(self):
        self._cambiar_ip()
        return self._verificar_cambio_cada_segundo(120)  # 2 minutos

    def _cambiar_ip(self):
        print("Intentando cambiar la IP con NordVPN...")
        try:
            subprocess.run([r"C:\Program Files\NordVPN\NordVPN.exe", "--connect"], check=False)
            print("Comando de NordVPN ejecutado. Esperando resultados...")
        except Exception as e:
            print(f"Error al intentar cambiar la IP: {e}")

    def _verificar_cambio_cada_segundo(self, duracion):
        for segundo in range(1, duracion + 1):
            try:
                nueva_ip = requests.get("https://api.ipify.org", timeout=10).text.strip()
                if nueva_ip != self.ip_inicial:
                    print(f"¡IP cambiada exitosamente a {nueva_ip} en el segundo {segundo}!")
                    return True
                else:
                    print(f"Segundo {segundo}/{duracion}: La IP no ha cambiado todavía.")
            except requests.RequestException as e:
                print(f"Error al intentar obtener la nueva IP en el segundo {segundo}: {e}")
            time.sleep(1)
        return False

    def _reiniciar_nordvpn(self):
        """
        Reinicia NordVPN utilizando taskkill y ejecuta el programa nuevamente.
        """
        print("Reiniciando NordVPN...")
        try:
            subprocess.run(["taskkill", "/F", "/IM", "NordVPN.exe"], check=False)
            time.sleep(5)  # Esperar un momento antes de reiniciar
            os.chdir(r"C:\Program Files\NordVPN")
            subprocess.run(["start", "NordVPN.exe"], check=False)
            print("NordVPN reiniciado.")
        except Exception as e:
            print(f"Error al reiniciar NordVPN: {e}")

    def _esperar_cambio_ip(self):
        print("Esperando a que otro proceso termine el cambio de IP...")
        for segundo in range(1, 61):  # Esperar hasta 1 minuto
            if not os.path.exists(CambiarIP.lock_file_path):
                print("El archivo de bloqueo ha sido eliminado. Verificando la IP...")
                nueva_ip = self._obtener_ip_inicial()
                if nueva_ip and nueva_ip != self.ip_inicial:
                    print(f"La IP ha cambiado exitosamente a {nueva_ip}.")
                else:
                    print("La IP no cambió después de que se eliminó el archivo de bloqueo.")
                return
            print(f"Segundo {segundo}/60: El archivo de bloqueo aún existe.")
            time.sleep(1)
        print("Tiempo de espera agotado. La IP no cambió o el archivo de bloqueo no fue eliminado.")

    def _eliminar_archivo_bloqueo(self):
        try:
            if os.path.exists(CambiarIP.lock_file_path):
                os.remove(CambiarIP.lock_file_path)
                print("Archivo de bloqueo eliminado.")
            else:
                print("No se encontró el archivo de bloqueo para eliminar.")
        except Exception as e:
            print(f"Error al intentar eliminar el archivo de bloqueo: {e}")


if __name__ == "__main__":
    CambiarIP()
