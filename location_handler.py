# -*- coding: utf-8 -*-
"""
Created on Sun Dec 15 01:19:13 2024

@author: andre
"""

import requests
import time
import json
from config import TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID
from adb_handler import ADB<PERSON>andler
from utils import print_current_time

class LocationFetcher:
    @staticmethod
    def get_primary_location():
        """Fetches primary location URL, latitude and longitude from env."""
        try:
          url_primary=  'https://map.fastpokemap.se/raid.json?'
          primary_lat =  35.6895
          primary_lon = 139.6917


          #url_primary=os.environ['URL_PRIMARY']
          #primary_lat = float(os.environ['PRIMARY_LAT'])
          #primary_lon = float(os.environ['PRIMARY_LON'])
          return  url_primary, primary_lat, primary_lon

        except KeyError as e:
           print(f"Error primary key not set {e}")
           return None,None,None

    @staticmethod
    def get_secondary_location():
       """Fetches secondary location URL, latitude and longitude from env."""
       try:
         url_secundaria=  'https://pogo.arspoofing.com/raids.json?'
         secondary_lat = 37.7749
         secondary_lon = -122.4194


         #url_secundaria= os.environ['URL_SECONDARY']
         #secondary_lat = float(os.environ['SECONDARY_LAT'])
         #secondary_lon = float(os.environ['SECONDARY_LON'])
         return  url_secundaria, secondary_lat, secondary_lon
       except KeyError as e:
          print(f"Error Secondary key not set {e}")
          return None,None,None

class LocationHandler:
  def __init__(self, device_id):
      self.device_id=device_id
      self.adb_handler = ADBHandler(device_id)

  def get_coordinates(self, url):
       """Retrieves raid coordinates from the given URL."""
       response = requests.get(url)
       coordinate_list = []
       if response.status_code == 200:
           data = response.json()
           for raid in data.get("raids", []):
              level = raid.get("level")
              latitude = raid.get("lat")
              longitude = raid.get("lng")
              if 5 == level  and time.time() > raid.get("raid_start"): #filtrando raid level 7 and time
                 coordinate_list.append((latitude, longitude))
           print_current_time(f"found: {coordinate_list}")
       return coordinate_list

  def find_nearest_coordinate(self, target_lat, target_lon, coordinates):
    """Finds the nearest coordinate from a list to the target location."""
    nearest_coordinate = None
    min_distance = float("inf")
    for lat, lon in coordinates:
        distance = ((lat - target_lat) ** 2 + (lon - target_lon) ** 2) ** 0.5
        if distance < min_distance:
           min_distance = distance
           nearest_coordinate = (lat, lon)
    return nearest_coordinate

  def teleport_to_location(self, lat, lon):
      """Teleports the device to the specified coordinates."""
      print_current_time(f"Teleporting to: lat={lat}, lon={lon}")
      #self.adb_handler.shell(f'am start-foreground-service -a com.evermorelabs.polygonsharp.TELEPORT --ef lat {lat} --ef lng {lon} --ef alt 0.0')
      #self.adb_handler.shell(f'am start-foreground-service -a theappninjas.gpsjoystick.TELEPORT --ef lat {lat} --ef lng {lon} --ef alt 0.0')
      self.adb_handler.shell(f'am start -W -a android.intent.action.VIEW -d https://fly.mg/coords?ll={lat},{lon}')


  def get_coordinate_with_fallback(self, target_lat=None, target_lon=None):
    """Retrieves coordinates using primary URL with fallback to secondary if needed."""
    if target_lat is None or target_lon is None:
         url_primaria, primary_lat, primary_lon = LocationFetcher.get_primary_location()
         target_lat = target_lat if target_lat is not None else primary_lat
         target_lon = target_lon if target_lon is not None else primary_lon
    else:
         url_primaria = LocationFetcher.get_primary_location()[0]

    timestamp_actual = str(time.time() * 1000).replace(".", "")
    coordinates_list = self.get_coordinates(url_primaria +"time="+timestamp_actual)

    if not coordinates_list:
         url_secundaria, _, _ = LocationFetcher.get_secondary_location()
         coordinates_list = self.get_coordinates(url_secundaria)
         if not coordinates_list:
            print("no coordinates found.")
            return None, None
    nearest_coordinate = self.find_nearest_coordinate(target_lat, target_lon, coordinates_list)
    if nearest_coordinate:
        self.teleport_to_location(nearest_coordinate[0], nearest_coordinate[1])
        return nearest_coordinate
    else:
      print("no nearest coordinate found.")
      return None,None


  def telegram_bot_sendtext(self, bot_message):
     bot_token = TELEGRAM_BOT_TOKEN
     bot_chatID = TELEGRAM_CHAT_ID
     send_text = 'https://api.telegram.org/bot' + bot_token + '/sendMessage?chat_id=' + bot_chatID + '&parse_mode=Markdown&text=' + bot_message
     response = requests.get(send_text)
     return response.json()