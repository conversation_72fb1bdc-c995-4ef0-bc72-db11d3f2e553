# -*- coding: utf-8 -*-
"""
Created on Sun Dec 15 01:22:31 2024

@author: andre
"""
import pytesseract
from config import TESSERACT_CMD
from PIL import Image
import cv2
class OCRHandler:
    def __init__(self):
         pytesseract.pytesseract.tesseract_cmd = TESSERACT_CMD

    def get_text_(self, screenshot_array):
     try:
        # Verifica si la imagen es válida
        if screenshot_array is None or screenshot_array.size == 0:
            raise ValueError("La imagen proporcionada está vacía o es inválida.")
        
        # Redimensionar la imagen a la mitad de su tamaño
        height, width = screenshot_array.shape[:2]
        reduced_image = cv2.resize(screenshot_array, (width // 2, height // 2))  # Reducir a la mitad

        # Preprocesamiento: convertir a escala de grises y binarizar la imagen
        gray_image = cv2.cvtColor(reduced_image, cv2.COLOR_BGR2GRAY)  # Convertir a escala de grises
        _, binary_image = cv2.threshold(gray_image, 128, 255, cv2.THRESH_BINARY)  # Binarización

        # Usar Tesseract con parámetros optimizados
        custom_config = r'--oem 3 --psm 6'  # OEM 3: LSTM OCR, PSM 6: Suponer una sola columna de texto
        text = pytesseract.image_to_string(binary_image, config=custom_config)
        
        # Si no se detecta texto, lanzar un error
        if not text.strip():
            raise ValueError("No se pudo extraer texto de la imagen.")

        return text

     except Exception as e:
        # Captura y muestra errores
        print(f"Error OCR: {e}")
        return ""  # Retorna cadena vacía si ocurre un error
    def get_text_coordinates(self, screenshot_array, target_text):
        try:
            data = pytesseract.image_to_data(screenshot_array, output_type=pytesseract.Output.DICT)
    
            for i in range(len(data["text"])):
                print(data["text"][i].strip())
                if target_text in data["text"][i].strip()  :  # Compara con el texto buscado
                    return data["left"][i], data["top"][i]  # Devuelve las coordenadas (x, y)
    
            return None  # Si no encuentra el texto, devuelve None
        except Exception as e:
            print(f"Error OCR : {e}")
            return None
    def get_text(self, screenshot_array):
     # Convertir directamente a escala de grises para reducir canales
     gray = cv2.cvtColor(screenshot_array, cv2.COLOR_BGR2GRAY)

     # Usar umbral adaptativo si hay variaciones de luz (más robusto)
     thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
                                   cv2.THRESH_BINARY, 15, 10)

     # Configuración rápida y eficiente para bloques de texto
     custom_config = r'--oem 1 --psm 6'  # oem 1 = Fast legacy + LSTM hybrid

     return pytesseract.image_to_string(thresh, config=custom_config)

               
    def get_text_old(self, screenshot_array):
         try:
             
             text = pytesseract.image_to_string(screenshot_array)
             return text
         except Exception as e:
             print(f"Error OCR : {e}")
             return ""
    def get_text_from_region(self, screenshot_array, x1, y1, x2, y2, config='--psm 10 --oem 3'):
        try:
             cropped_image = screenshot_array[y1:y2, x1:x2]
             gray = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2GRAY)

             # Usar umbral adaptativo si hay variaciones de luz (más robusto)
             thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
                                           cv2.THRESH_BINARY, 15, 10)

             # Configuración rápida y eficiente para bloques de texto
             custom_config = r'--oem 1 --psm 6'  # oem 1 = Fast legacy + LSTM hybrid

             return pytesseract.image_to_string(thresh, config=custom_config)


        except Exception as e:
            print(f"Error region OCR : {e}")
            return ""         
    def get_text_from_regio_(self, screenshot_array, x1, y1, x2, y2, config='--psm 10 --oem 3'):
        try:
             cropped_image = screenshot_array[y1:y2, x1:x2]
             text = pytesseract.image_to_string(cropped_image, config=config)
             return text
        except Exception as e:
            print(f"Error region OCR : {e}")
            return ""
    def get_text_data(self,screenshot_array,config='--oem 3 --psm 6'):
         try:
             gray = cv2.cvtColor(screenshot_array, cv2.COLOR_BGR2GRAY)
             text_data = pytesseract.image_to_data(gray, config=config, output_type=pytesseract.Output.DICT)
             return text_data
         except Exception as e:
             print(f"Error getting  OCR Data {e}")
             return ""