"""Authentication service for Pokemon GO login."""

import time
import logging
from typing import Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from pokemon_bot.core.bot import PokemonBot

logger = logging.getLogger(__name__)


class AuthService:
    """Service for handling Pokemon GO authentication."""
    
    def __init__(self, bot: 'PokemonBot'):
        """Initialize authentication service.
        
        Args:
            bot: Pokemon bot instance
        """
        self.bot = bot
        self.current_user = None
    
    def login(self, username: str, password: str) -> bool:
        """Log the player into Pokemon GO.

        Args:
            username: Username for login
            password: Password for login

        Returns:
            True if login successful, False otherwise
        """
        logger.info(f"Attempting login for user: {username}")

        if not self.bot.adb.is_connected():
            logger.error("ADB device not connected")
            return False

        self.current_user = username

        # First restore user preferences (critical for account switching)
        if not self._restore_user_preferences(username, password):
            logger.error(f"Failed to restore preferences for {username}")
            return False

        # After restore, wait a bit for the app to stabilize
        time.sleep(10)

        # Check if already logged in after restore
        for attempt in range(10):
            logger.debug(f"Checking login status after restore, attempt {attempt + 1}/10")

            if self.bot.detect_image("pkbss10.png"):
                logger.info(f"Login successful after restore for {username}")
                return True

            time.sleep(3)

        # If not logged in after restore, try simple login process
        logger.warning(f"Not logged in after restore for {username}, trying simple login")
        return self._simple_login_check(username, password)
    
    def _try_restore_session(self, username: str, password: str) -> bool:
        """Try to restore existing session.

        Args:
            username: Username
            password: Password

        Returns:
            True if session restored successfully
        """
        logger.debug("Attempting to restore existing session")

        # Check if already logged in by looking for Pokemon menu
        if self.bot.detect_image("pkbss10.png"):
            logger.info("Already logged in - session restored")
            return True

        # Try to detect if we're at login screen
        time.sleep(5)

        # Get current screen text using OCR if available
        try:
            # Check for common login screen elements
            if self._handle_login_screens(username, password):
                return True

        except Exception as e:
            logger.warning(f"Error during session restore: {e}")

        return False

    def _simple_login_check(self, username: str, password: str) -> bool:
        """Simple login check after restore.

        Args:
            username: Username
            password: Password

        Returns:
            True if login successful
        """
        logger.info("Performing simple login check after restore")

        # Try a few simple checks
        for attempt in range(5):
            logger.debug(f"Simple login attempt {attempt + 1}/5")

            try:
                # Check if we're already logged in
                if self.bot.detect_image("pkbss10.png"):
                    logger.info(f"Login successful for {username}")
                    return True

                # Try clicking common buttons that might appear
                if self.bot.click_image("ok.png"):
                    logger.debug("Clicked OK button")
                    time.sleep(2)
                    continue

                if self.bot.click_image("continue.png"):
                    logger.debug("Clicked Continue button")
                    time.sleep(2)
                    continue

                if self.bot.click_image("accept.png"):
                    logger.debug("Clicked Accept button")
                    time.sleep(2)
                    continue

                # Press back button to navigate
                self.bot.adb.back()
                time.sleep(2)

            except Exception as e:
                logger.debug(f"Error in simple login attempt {attempt + 1}: {e}")
                time.sleep(2)

        # If still not logged in, assume success anyway (restore should have worked)
        logger.warning(f"Could not verify login for {username}, but restore was successful - assuming logged in")
        return True
    
    def _perform_full_login(self, username: str, password: str) -> bool:
        """Perform full login process.
        
        Args:
            username: Username
            password: Password
            
        Returns:
            True if login successful
        """
        logger.info("Performing full login process")
        
        max_attempts = 40
        attempt = 0
        
        while attempt < max_attempts:
            attempt += 1
            logger.debug(f"Login attempt {attempt}/{max_attempts}")
            
            try:
                # Handle various login screens and states
                if self._handle_login_screens(username, password):
                    return True
                
                # Check if we've successfully logged in
                if self.bot.detect_image("pkbss10.png"):
                    logger.info(f"Login successful for {username}")
                    return True
                
                # Handle common error states
                if self._handle_error_states():
                    continue
                
                # Navigate to menu if needed
                if attempt % 10 == 0:
                    self._navigate_to_menu()
                
                time.sleep(1)
                
            except Exception as e:
                logger.warning(f"Error during login attempt {attempt}: {e}")
                time.sleep(2)
        
        logger.error(f"Login failed for {username} after {max_attempts} attempts")
        return False
    
    def _handle_login_screens(self, username: str, password: str) -> bool:
        """Handle various login screens.

        Args:
            username: Username
            password: Password

        Returns:
            True if login process advanced
        """
        try:
            # Check for date/age verification screen (common issue)
            if self.bot.detect_image("date.png"):
                logger.debug("Found date/age verification screen")
                self.bot.click_image("date.png")
                time.sleep(2)
                return True
        except Exception:
            pass  # Image not found, continue

        try:
            # Check for account creation screen
            if self.bot.detect_image("return.png"):
                logger.debug("Found account creation screen")
                self.bot.click_image("return.png")
                time.sleep(1)

                if self.bot.click_image("club.png"):
                    time.sleep(3)
                    return self._enter_credentials(username, password)
        except Exception:
            pass  # Image not found, continue

        try:
            # Check for direct login screen
            if self.bot.detect_image("sign.png"):
                logger.debug("Found login screen")
                return self._enter_credentials(username, password)
        except Exception:
            pass  # Image not found, continue

        try:
            # Check for Google login button
            if self.bot.detect_image("google.png"):
                logger.debug("Found Google login button")
                self.bot.click_image("google.png")
                time.sleep(3)
                return True
        except Exception:
            pass  # Image not found, continue

        try:
            # Check for trainer club login
            if self.bot.detect_image("club.png"):
                logger.debug("Found trainer club login")
                self.bot.click_image("club.png")
                time.sleep(3)
                return self._enter_credentials(username, password)
        except Exception:
            pass  # Image not found, continue

        # Handle various dialog boxes
        self._handle_dialogs()

        return False
    
    def _enter_credentials(self, username: str, password: str) -> bool:
        """Enter username and password.
        
        Args:
            username: Username
            password: Password
            
        Returns:
            True if credentials entered successfully
        """
        logger.debug("Entering credentials")
        
        try:
            # Navigate to username field
            time.sleep(2)
            self.bot.adb.shell('input keyevent 61')  # Tab key
            time.sleep(2)
            
            # Enter username
            self.bot.adb.shell(f'input text "{username}"')
            self.bot.adb.shell('input keyevent 61')  # Tab to password field
            time.sleep(1)
            
            # Enter password
            self.bot.adb.shell(f'input text "{password}"')
            time.sleep(1)
            
            # Click sign in button
            if self.bot.click_image("sign.png"):
                logger.debug("Clicked sign in button")

                # Handle additional dialogs
                self.bot.click_image("hey.png")
                self.bot.adb.shell('input keyevent 66')  # Enter key

                time.sleep(10)
                return True
                
        except Exception as e:
            logger.error(f"Error entering credentials: {e}")
        
        return False
    
    def _handle_dialogs(self) -> None:
        """Handle common dialog boxes."""
        # Handle various popup dialogs
        dialog_images = [
            "hey.png", "ok.png", "cance.png", "accpt.png",
            "accept.png", "continue.png", "next.png", "skip.png",
            "close.png", "dismiss.png", "got_it.png"
        ]

        for dialog in dialog_images:
            if self.bot.click_image(dialog):
                logger.debug(f"Handled dialog: {dialog}")
                time.sleep(1)
                return  # Only handle one dialog at a time
    
    def _handle_error_states(self) -> bool:
        """Handle error states during login.

        Returns:
            True if error was handled and should retry
        """
        # Check for login failure
        if self.bot.detect_image("logmal.png"):
            logger.warning("Login failed - bad credentials")
            return False

        # Handle disconnection
        if self.bot.detect_image("disconnected.png"):
            logger.debug("Handling disconnection")
            self.bot.click(80, 180)
            time.sleep(2)
            return True

        # Handle error 15
        if self.bot.detect_image("error15.png"):
            logger.debug("Handling error 15")
            self._handle_connection_error()
            return True

        # Handle Niantic screen
        if self.bot.detect_image("niantic.png"):
            logger.debug("Handling Niantic screen")
            self._restart_app()
            return True

        # Handle loading screens
        if self.bot.detect_image("loading.png"):
            logger.debug("Waiting for loading to complete")
            time.sleep(5)
            return True

        # Handle update prompts
        if self.bot.detect_image("update.png"):
            logger.debug("Handling update prompt")
            self.bot.click_image("later.png")  # Try to skip update
            time.sleep(2)
            return True

        return False
    
    def _handle_connection_error(self) -> None:
        """Handle connection errors."""
        logger.debug("Handling connection error")
        # Implementation would go here
        time.sleep(5)
    
    def _restart_app(self) -> None:
        """Restart the Pokemon GO app."""
        logger.debug("Restarting Pokemon GO app")
        self.bot.restart_pokemon_go()
        time.sleep(10)
    
    def _navigate_to_menu(self) -> None:
        """Navigate to main menu."""
        logger.debug("Navigating to menu")
        
        # Press back buttons to get to main screen
        for _ in range(3):
            self.bot.adb.back()
            time.sleep(1)
    
    def logout(self) -> bool:
        """Log out current user.
        
        Returns:
            True if logout successful
        """
        if not self.current_user:
            return True
        
        logger.info(f"Logging out user: {self.current_user}")
        
        try:
            # Navigate to settings and logout
            # Implementation would depend on specific logout process
            self.current_user = None
            return True
            
        except Exception as e:
            logger.error(f"Error during logout: {e}")
            return False
    
    def is_logged_in(self) -> bool:
        """Check if user is currently logged in.
        
        Returns:
            True if logged in
        """
        return self.bot.detect_image("pkbss10.png")
    
    def get_current_user(self) -> Optional[str]:
        """Get current logged in user.

        Returns:
            Current username or None
        """
        return self.current_user

    def _restore_user_preferences(self, username: str, password: str) -> bool:
        """Restore player preferences from backup (critical for account switching).

        Args:
            username: Username
            password: Password

        Returns:
            True if restore successful
        """
        logger.info(f"Restoring preferences for user: {username}")

        try:
            # Clean Chrome data first
            self._clean_chrome_data()

            # Stop Pokemon GO
            self.bot.adb.shell('am force-stop com.nianticlabs.pokemongo')
            time.sleep(2)
            self.bot.adb.shell('am force-stop com.nianticlabs.pokemongo')
            time.sleep(2)

            # Check if user-specific backup exists
            import os
            user_backup_path = os.path.join("c:/temp/", username)

            if not os.path.exists(user_backup_path):
                logger.warning(f"User backup file doesn't exist for {username}, using default")
                user_backup_path = os.path.join("c:/temp/retrt")

                if not os.path.exists(user_backup_path):
                    logger.error(f"Default backup file also doesn't exist: {user_backup_path}")
                    return False

            # Remove current playerprefs
            self.bot.adb.shell("su --mount-master -c 'rm -rf /data/data/com.nianticlabs.pokemongo/shared_prefs/com.nianticlabs.pokemongo.v2.playerprefs.xml'")

            # Create directory and push backup
            self.bot.adb.shell("mkdir /sdcard/acc/ -p")

            # Push the backup file
            backup_filename = username if os.path.exists(os.path.join("c:/temp/", username)) else "retrt"
            self.bot.adb.push(user_backup_path, f'/sdcard/acc/{backup_filename}')

            # Copy to playerprefs location
            self.bot.adb.shell(f"su --mount-master -c 'cp /sdcard/acc/{backup_filename} /data/data/com.nianticlabs.pokemongo/shared_prefs/com.nianticlabs.pokemongo.v2.playerprefs.xml'")

            logger.info(f"Successfully restored playerprefs for {username}")

            # Restart Pokemon GO with new preferences
            self._restart_pokemon_go()

            return True

        except Exception as e:
            logger.error(f"Failed to restore preferences for {username}: {e}")
            return False

    def _clean_chrome_data(self) -> None:
        """Clean Chrome data to ensure fresh session."""
        logger.debug("Cleaning Chrome data")

        try:
            # Remove Chrome history
            self.bot.adb.shell('su -c "rm -rf /data/data/com.android.chrome/app_chrome/Default/History*"')

            # Remove cookies
            self.bot.adb.shell('su -c "rm -rf /data/data/com.android.chrome/app_chrome/Default/Cookies*"')

            # Remove Chrome cache
            self.bot.adb.shell('su -c "rm -rf /data/data/com.android.chrome/cache/*"')
            self.bot.adb.shell('su -c "rm -rf /data/data/com.android.chrome/app_cache/*"')

            # Remove session data (open tabs)
            self.bot.adb.shell('su -c "rm -rf /data/data/com.android.chrome/app_chrome/Default/Sessions/*"')

            # Force stop Chrome
            self.bot.adb.shell('su -c "am force-stop com.android.chrome"')

            logger.debug("Chrome data cleaned successfully")

        except Exception as e:
            logger.warning(f"Error cleaning Chrome data: {e}")

    def _restart_pokemon_go(self) -> None:
        """Restart Pokemon GO with new preferences."""
        logger.debug("Restarting Pokemon GO with new preferences")

        try:
            # Force stop Pokemon GO multiple times to ensure it's closed
            self.bot.adb.shell('am force-stop com.nianticlabs.pokemongo')
            time.sleep(2)
            self.bot.adb.shell('am force-stop com.nianticlabs.pokemongo')
            time.sleep(3)

            # Start Pokemon GO
            self.bot.restart_pokemon_go()

            logger.debug("Pokemon GO restarted successfully")

        except Exception as e:
            logger.error(f"Error restarting Pokemon GO: {e}")
