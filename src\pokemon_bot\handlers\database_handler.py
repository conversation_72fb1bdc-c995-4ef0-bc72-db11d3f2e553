"""Database handler for MongoDB operations."""

import time
from typing import Optional, Dict, Any, List
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

from ..core.config import get_config
from ..core.exceptions import DatabaseError
from ..core.logging import get_logger
from ..core.retry import retry

logger = get_logger(__name__)


class DatabaseHandler:
    """Enhanced MongoDB handler with connection pooling and error handling."""
    
    def __init__(self):
        """Initialize database handler."""
        self.config = get_config()
        self._client: Optional[MongoClient] = None
        self._db = None
        self._connect()
    
    def _connect(self) -> None:
        """Establish database connection."""
        try:
            if not self.config.mongodb.connection_string:
                logger.warning("MongoDB connection string not configured - database features disabled")
                return

            self._client = MongoClient(
                self.config.mongodb.connection_string,
                serverSelectionTimeoutMS=self.config.mongodb.server_selection_timeout,
                connectTimeoutMS=self.config.mongodb.connection_timeout,
                retryWrites=True
            )

            # Test connection
            self._client.admin.command('ping')

            self._db = self._client[self.config.mongodb.database]
            logger.info("Successfully connected to MongoDB")

        except Exception as e:
            logger.warning(f"Failed to connect to MongoDB: {e} - database features disabled")
            self._client = None
            self._db = None
    
    @property
    def db(self):
        """Get database instance."""
        if not self._db:
            self._connect()
        return self._db

    def is_connected(self) -> bool:
        """Check if database is connected."""
        return self._db is not None
    
    @retry(max_attempts=3, exceptions=DatabaseError)
    def find_user(self, username: str) -> Optional[Dict[str, Any]]:
        """Find user by username.

        Args:
            username: Username to search for

        Returns:
            User document or None if not found
        """
        if not self.is_connected():
            logger.warning("Database not connected - cannot find user")
            return None

        try:
            collection = self.db[self.config.mongodb.collections["accounts"]]
            user = collection.find_one({"user": username})
            return user
        except Exception as e:
            logger.error(f"Failed to find user {username}: {e}")
            return None
    
    @retry(max_attempts=3, exceptions=DatabaseError)
    def update_user_status(self, username: str, status: str, **kwargs) -> bool:
        """Update user status.
        
        Args:
            username: Username to update
            status: New status
            **kwargs: Additional fields to update
            
        Returns:
            True if update successful
        """
        try:
            collection = self.db[self.config.mongodb.collections["accounts"]]
            
            update_data = {
                "status": status,
                "time_updated": time.time(),
                **kwargs
            }
            
            result = collection.update_one(
                {"user": username},
                {"$set": update_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            raise DatabaseError(f"Failed to update user status: {e}") from e
    
    @retry(max_attempts=3, exceptions=DatabaseError)
    def create_user(self, username: str, **kwargs) -> bool:
        """Create new user.
        
        Args:
            username: Username
            **kwargs: Additional user data
            
        Returns:
            True if creation successful
        """
        try:
            collection = self.db[self.config.mongodb.collections["accounts"]]
            
            user_data = {
                "user": username,
                "status": False,
                "create": True,
                "time_updated": time.time(),
                "status_ban": False,
                **kwargs
            }
            
            result = collection.insert_one(user_data)
            return result.inserted_id is not None
            
        except Exception as e:
            raise DatabaseError(f"Failed to create user: {e}") from e
    
    @retry(max_attempts=3, exceptions=DatabaseError)
    def log_adventure(self, username: str, adventure_type: str, success: bool, duration: float, **kwargs) -> None:
        """Log adventure result.
        
        Args:
            username: Username
            adventure_type: Type of adventure
            success: Whether adventure was successful
            duration: Adventure duration in seconds
            **kwargs: Additional log data
        """
        try:
            collection = self.db[self.config.mongodb.collections["logs"]]
            
            log_data = {
                "user": username,
                "adventure_type": adventure_type,
                "success": success,
                "duration": duration,
                "timestamp": time.time(),
                **kwargs
            }
            
            collection.insert_one(log_data)
            
        except Exception as e:
            logger.error(f"Failed to log adventure: {e}")
            # Don't raise exception for logging failures
    
    @retry(max_attempts=3, exceptions=DatabaseError)
    def get_user_stats(self, username: str) -> Dict[str, Any]:
        """Get user statistics.
        
        Args:
            username: Username
            
        Returns:
            Dictionary with user statistics
        """
        try:
            logs_collection = self.db[self.config.mongodb.collections["logs"]]
            
            # Aggregate user statistics
            pipeline = [
                {"$match": {"user": username}},
                {"$group": {
                    "_id": "$adventure_type",
                    "total_attempts": {"$sum": 1},
                    "successful_attempts": {"$sum": {"$cond": ["$success", 1, 0]}},
                    "total_duration": {"$sum": "$duration"},
                    "avg_duration": {"$avg": "$duration"}
                }}
            ]
            
            stats = list(logs_collection.aggregate(pipeline))
            
            # Format statistics
            formatted_stats = {
                "username": username,
                "adventures": {}
            }
            
            for stat in stats:
                adventure_type = stat["_id"]
                formatted_stats["adventures"][adventure_type] = {
                    "total_attempts": stat["total_attempts"],
                    "successful_attempts": stat["successful_attempts"],
                    "success_rate": stat["successful_attempts"] / stat["total_attempts"] if stat["total_attempts"] > 0 else 0,
                    "total_duration": stat["total_duration"],
                    "avg_duration": stat["avg_duration"]
                }
            
            return formatted_stats
            
        except Exception as e:
            raise DatabaseError(f"Failed to get user stats: {e}") from e
    
    @retry(max_attempts=3, exceptions=DatabaseError)
    def get_active_users(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get list of active users.
        
        Args:
            limit: Maximum number of users to return
            
        Returns:
            List of active user documents
        """
        try:
            collection = self.db[self.config.mongodb.collections["accounts"]]
            
            users = list(collection.find(
                {"status": True, "status_ban": {"$ne": True}},
                limit=limit
            ))
            
            return users
            
        except Exception as e:
            raise DatabaseError(f"Failed to get active users: {e}") from e
    
    def health_check(self) -> Dict[str, Any]:
        """Check database health.
        
        Returns:
            Dictionary with health status
        """
        try:
            # Test connection
            self._client.admin.command('ping')
            
            # Get server info
            server_info = self._client.server_info()
            
            # Get database stats
            db_stats = self.db.command("dbStats")
            
            return {
                "status": "healthy",
                "server_version": server_info.get("version"),
                "database": self.config.mongodb.database,
                "collections": db_stats.get("collections", 0),
                "data_size": db_stats.get("dataSize", 0),
                "storage_size": db_stats.get("storageSize", 0)
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    def close(self) -> None:
        """Close database connection."""
        if self._client:
            self._client.close()
            self._client = None
            self._db = None
            logger.info("Database connection closed")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()
