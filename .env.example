# Environment Variables Example
# Copy this file to .env and fill in your actual values

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here

# MongoDB Configuration
MONGODB_CONNECTION_STRING=mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority

# API Configuration
API_BASE_URL=http://localhost:5001
API_TIMEOUT=30

# Security
SECRET_KEY=your_secret_key_here
ENCRYPT_CREDENTIALS=true

# Development Settings
DEBUG=false
LOG_LEVEL=INFO

# Device Configuration
DEFAULT_DEVICE_ID=************:5555
MAX_CONCURRENT_DEVICES=5

# Performance Settings
MAX_WORKERS=4
MEMORY_LIMIT_MB=512

# Paths (Windows example)
TESSERACT_CMD=C:/Program Files (x86)/Tesseract-OCR/tesseract.exe
TEMP_DIR=c:/temp/
LOG_DIR=c:/temp/logs/
