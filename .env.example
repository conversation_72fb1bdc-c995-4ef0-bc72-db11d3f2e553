# Environment Variables - Configured with actual project credentials
# This file contains the working configuration from the original project

# Telegram Bot Configuration (from original project)
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=-1001782327359

# MongoDB Configuration (from original project)
MONGODB_CONNECTION_STRING=mongodb+srv://pokebot:<EMAIL>/?retryWrites=true&w=majority

# API Configuration
API_BASE_URL=http://localhost:5001
API_TIMEOUT=30

# Security
SECRET_KEY=pokemon_bot_secret_key_2024
ENCRYPT_CREDENTIALS=true

# Development Settings
DEBUG=true
LOG_LEVEL=DEBUG

# Device Configuration
DEFAULT_DEVICE_ID=************:5555
MAX_CONCURRENT_DEVICES=5

# Performance Settings
MAX_WORKERS=4
MEMORY_LIMIT_MB=512

# Paths (Windows configuration from original project)
TESSERACT_CMD=C:/Program Files (x86)/Tesseract-OCR/tesseract.exe
TEMP_DIR=c:/temp/
LOG_DIR=c:/temp/logs/
