# -*- coding: utf-8 -*-
"""
Created on Thu Mar  6 03:54:24 2025

@author: andre
"""
import sys
import requests
import time
from ppadb.client import Client
from location_fetcher import LocationFetcher


class Teleporter:
    def __init__(self, device_id, pokedex_number):
        self.adb = Client(host='localhost', port=5037)
        self.device = self.adb.device(device_id)
        self.pokedex_number = str(pokedex_number)  # Convertimos a string por compatibilidad

    def get_coor(self, url):
        """ Obtiene las coordenadas de incursiones del Pokémon especificado """
        response = requests.get(url)
        coordinate_list = []

        if response.status_code == 200:
            data = response.json()

            for raid in data.get("battles", []):
                level = raid.get("level")
                latitude = raid.get("lat")
                longitude = raid.get("lng")
                numero = str(raid.get("pokemon_id"))

                if numero == self.pokedex_number and time.time() > raid.get("raid_start"):
                    coordinate_list.append((latitude, longitude))

        return coordinate_list

    def find_nearest_coordinate(self, target_lat, target_lon, coordinates):
        """ Encuentra la coordenada más cercana al objetivo """
        nearest_coordinate = None
        min_distance = float("inf")

        for lat, lon in coordinates:
            distance = ((lat - target_lat) ** 2 + (lon - target_lon) ** 2) ** 0.5

            if distance < min_distance:
                min_distance = distance
                nearest_coordinate = (lat, lon)

        return nearest_coordinate
        #,coordinates[-1]

    def teleport_to_location(self, lat, lon):
        """ Teletransporta el dispositivo a la ubicación dada usando ADB """
        self.device.shell(f'am start -W -a android.intent.action.VIEW -d https://fly.mg/coords?ll={lat},{lon}')
        print(f"Teletransportando a {lat}, {lon} (Pokédex ID: {self.pokedex_number})")
        print(f'am start -W -a android.intent.action.VIEW -d https://fly.mg/coords?ll={lat},{lon}')
 
    def teleport_pokemon(self):
        """ Lógica principal para encontrar incursiones y teletransportar """
        print(f"Buscando incursiones de Pokédex ID: {self.pokedex_number}...")

        url_primaria, target_lat, target_lon = LocationFetcher.get_primary_location()
        
        import datetime
        fecha_hora_actual = datetime.datetime.now()
        timestamp_actual = str(fecha_hora_actual.timestamp() * 1000).replace(".", "")

        coordinates_list = self.get_coor(url_primaria + "time=" + timestamp_actual)

        if not coordinates_list:
            print("No se encontraron incursiones en la ubicación primaria. Probando ubicación secundaria...")
            url_sec, target_lat, target_lon = LocationFetcher.get_secondary_location()
            coordinates_list = self.get_coor(url_sec)

        if coordinates_list:
            nearest_coordinate = self.find_nearest_coordinate(target_lat, target_lon, coordinates_list)
            self.teleport_to_location(nearest_coordinate[0], nearest_coordinate[1])
        else:
            print(f"No se encontraron incursiones activas de Pokédex ID: {self.pokedex_number}.")


# Solo ejecuta la función si este archivo se ejecuta directamente
"""
if __name__ == "__main__":
    if len(sys.argv) < 1:
        print("Uso: python teleport.py <DEVICE_ID> <POKEDEX_ID>")
        sys.exit(1)

        id_ = sys.argv[1]
        pokedex_id = sys.argv[2]
    id_="R28M20D0KGW"
    ip_list =["R28M13BMAWJ",     
    "R28M13BNE3M",     
    "R28M20D0KGW",     
    "R28M20D0N3L",     
    "R28M20P9AQL",     
    "R28M22DWS4A",     
    "R28M31S21ZH",     
    "R58M47JLHPH",     
    "R58M545NXMA",     
    "R58M84MSD2K",     
    "R58MA3VY8BF",     
    "R58MA7FLGLH",     
    "R58MA7FN58B",     
    "R58MB1CKKFJ",     
    "R58MB1CLS6K",     
    "R58N3495B0P",
    "R28M336H54P",
    "R58M55869YD",
    "R58M7408G3A"
              ]
    for item in ip_list:
        pokedex_id=6
        teleporter = Teleporter(item, pokedex_id)
        #teleporter.teleport_to_location("40.7565","-73.9791")
        teleporter.teleport_to_location("40.753364","-73.988699")
        #teleporter.teleport_to_location("-33.8635","151.2092")
        #teleporter.teleport_to_location("40.7583","-73.9842")
        
        
        #time.sleep(10)
        #teleporter.teleport_to_location("-33.854591","151.150853")
        #teleporter.teleport_to_location("-33.85463", "151.173813")
        #teleporter.teleport_pokemon()
"""
if __name__ == "__main__":
    print("Uso: python teleport.py <DEVICE_ID> <POKEDEX_ID>")
    if len(sys.argv) < 1:
        print("Uso: python teleport.py <DEVICE_ID> <POKEDEX_ID>")
        sys.exit(1)

        id_ = sys.argv[1]
        pokedex_id = sys.argv[2]
        
id_="R28M20D0KGW"
pokedex_id=6
teleporter = Teleporter(id_, pokedex_id)     
teleporter.teleport_pokemon()   

        