"""Main Pokemon Bot class that orchestrates all components."""

import time
from typing import Optional, Dict, Any, List
from pathlib import Path

from .config import get_config
from .exceptions import PokemonBotError, DeviceError
from .logging import get_logger
from .retry import retry, get_error_handler

from ..handlers.adb_handler import ADB<PERSON>andler
from ..handlers.image_handler import ImageHandler
from ..handlers.ocr_handler import OCRHandler
from ..handlers.screenshot_handler import <PERSON>shotHandler
from ..handlers.database_handler import DatabaseHandler
from ..handlers.telegram_handler import TelegramHandler

from ..services.device_manager import DeviceManager
from ..services.user_service import UserService
from ..services.adventure_service import AdventureService

logger = get_logger(__name__)


class PokemonBot:
    """Main Pokemon Bot class that coordinates all operations."""
    
    def __init__(self, device_id: str, config_override: Optional[Dict[str, Any]] = None):
        """Initialize Pokemon Bot.
        
        Args:
            device_id: Android device identifier
            config_override: Optional configuration overrides
        """
        self.device_id = device_id
        self.config = get_config()
        
        # Apply configuration overrides
        if config_override:
            for key, value in config_override.items():
                self.config.set(key, value)
        
        # Initialize logger with device context
        logger.add_device_context(device_id)
        
        # Initialize handlers
        self._initialize_handlers()
        
        # Initialize services
        self._initialize_services()
        
        # Bot state
        self.is_running = False
        self.current_user = None
        self.session_start_time = None
        
        logger.info(f"Pokemon Bot initialized for device {device_id}")
    
    def _initialize_handlers(self) -> None:
        """Initialize all handlers."""
        try:
            # Core handlers
            self.adb = ADBHandler(self.device_id)
            self.image_handler = ImageHandler()
            self.ocr_handler = OCRHandler()
            self.screenshot_handler = ScreenshotHandler(self.device_id)
            
            # Optional handlers (may be disabled in config)
            try:
                self.database = DatabaseHandler()
                if not self.database.is_connected():
                    self.database = None
                    logger.warning("Database handler disabled - MongoDB connection failed")
            except Exception as e:
                self.database = None
                logger.warning(f"Database handler disabled - initialization failed: {e}")
            
            if self.config.telegram.enabled and self.config.telegram.bot_token:
                self.telegram = TelegramHandler()
            else:
                self.telegram = None
                logger.warning("Telegram handler disabled")
            
        except Exception as e:
            raise PokemonBotError(f"Failed to initialize handlers: {e}") from e
    
    def _initialize_services(self) -> None:
        """Initialize all services."""
        try:
            self.device_manager = DeviceManager(self.adb)
            self.user_service = UserService(self.database) if self.database else None
            self.adventure_service = AdventureService(self)
            
        except Exception as e:
            raise PokemonBotError(f"Failed to initialize services: {e}") from e
    
    @retry(max_attempts=3, exceptions=DeviceError)
    def start_session(self, user: str, password: str) -> bool:
        """Start a bot session for a user.
        
        Args:
            user: Username
            password: Password
            
        Returns:
            True if session started successfully
        """
        try:
            self.session_start_time = time.time()
            self.current_user = user
            
            logger.info(f"Starting session for user {user}")
            
            # Setup device
            if not self._setup_device():
                raise DeviceError("Failed to setup device")
            
            # Login user
            if not self._login_user(user, password):
                raise PokemonBotError(f"Login failed for user {user}")
            
            self.is_running = True
            
            # Update user status in database
            if self.user_service:
                self.user_service.update_user_status(user, "active")
            
            # Send notification
            if self.telegram:
                self.telegram.send_message(f"Session started for user {user}")
            
            logger.info(f"Session started successfully for user {user}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start session for user {user}: {e}", exc_info=True)
            self.stop_session()
            raise
    
    def stop_session(self) -> None:
        """Stop the current bot session."""
        if not self.is_running:
            return
        
        try:
            logger.info(f"Stopping session for user {self.current_user}")
            
            # Update user status
            if self.user_service and self.current_user:
                self.user_service.update_user_status(self.current_user, "inactive")
            
            # Calculate session duration
            if self.session_start_time:
                duration = time.time() - self.session_start_time
                logger.info(f"Session duration: {duration:.2f} seconds")
            
            # Send notification
            if self.telegram and self.current_user:
                self.telegram.send_message(f"Session ended for user {self.current_user}")
            
            self.is_running = False
            self.current_user = None
            self.session_start_time = None
            
        except Exception as e:
            logger.error(f"Error stopping session: {e}", exc_info=True)
    
    def _setup_device(self) -> bool:
        """Setup device for bot operations."""
        try:
            # Disable screen rotation
            self.adb.shell("content insert --uri content://settings/system --bind name:s:accelerometer_rotation --bind value:i:0")
            self.adb.shell("content insert --uri content://settings/system --bind name:s:user_rotation --bind value:i:0")
            
            # Get device info
            device_info = self.adb.get_device_info()
            logger.info(f"Device info: {device_info}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup device: {e}")
            return False
    
    def _login_user(self, user: str, password: str) -> bool:
        """Login user to the game."""
        try:
            # This would contain the actual login logic
            # For now, we'll just return True as a placeholder
            logger.info(f"Logging in user {user}")
            
            # TODO: Implement actual login logic using image detection and OCR
            # This would involve:
            # 1. Taking screenshot
            # 2. Detecting login screen elements
            # 3. Entering credentials
            # 4. Verifying successful login
            
            return True
            
        except Exception as e:
            logger.error(f"Login failed for user {user}: {e}")
            return False
    
    def run_adventure(self, adventure_type: str, **kwargs) -> bool:
        """Run a specific adventure.
        
        Args:
            adventure_type: Type of adventure to run
            **kwargs: Adventure-specific parameters
            
        Returns:
            True if adventure completed successfully
        """
        if not self.is_running:
            raise PokemonBotError("Bot session not started")
        
        return self.adventure_service.run_adventure(adventure_type, self.current_user, **kwargs)
    
    def take_screenshot(self) -> str:
        """Take a screenshot and return the file path."""
        return self.screenshot_handler.capture()
    
    def detect_image(self, template_name: str, **kwargs) -> Optional[tuple]:
        """Detect an image on the current screen."""
        screenshot = self.adb.get_cv2_image()
        return self.image_handler.detect(screenshot, template_name, **kwargs)
    
    def extract_text(self, region: Optional[tuple] = None, **kwargs) -> str:
        """Extract text from the current screen."""
        screenshot = self.adb.get_cv2_image()
        return self.ocr_handler.extract_text(screenshot, region=region, **kwargs)
    
    def click(self, x: int, y: int, delay: float = 1.0) -> None:
        """Click at coordinates with optional delay."""
        self.adb.tap(x, y)
        if delay > 0:
            time.sleep(delay)
    
    def click_image(self, template_name: str, timeout: int = 10, **kwargs) -> bool:
        """Click on an image if found.
        
        Args:
            template_name: Name of template image
            timeout: Maximum time to wait for image
            **kwargs: Additional detection parameters
            
        Returns:
            True if image was found and clicked
        """
        def check_and_click():
            coords = self.detect_image(template_name, **kwargs)
            if coords:
                self.click(coords[0], coords[1])
                return True
            return False
        
        return self.adb.wait_for_element(check_and_click, timeout)
    
    def wait_for_text(self, text: str, timeout: int = 30, **kwargs) -> bool:
        """Wait for specific text to appear on screen.
        
        Args:
            text: Text to wait for
            timeout: Maximum wait time in seconds
            **kwargs: OCR parameters
            
        Returns:
            True if text found within timeout
        """
        def check_text():
            return self.ocr_handler.is_text_present(
                self.adb.get_cv2_image(), text, **kwargs
            )
        
        return self.adb.wait_for_element(check_text, timeout)
    
    def send_notification(self, message: str, image_path: Optional[str] = None) -> None:
        """Send notification via Telegram.
        
        Args:
            message: Message to send
            image_path: Optional image to send with message
        """
        if self.telegram:
            if image_path:
                self.telegram.send_photo(image_path, message)
            else:
                self.telegram.send_message(message)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current bot status.
        
        Returns:
            Dictionary with bot status information
        """
        status = {
            "device_id": self.device_id,
            "is_running": self.is_running,
            "current_user": self.current_user,
            "session_duration": None,
            "device_connected": self.adb.is_connected() if self.adb else False,
        }
        
        if self.session_start_time:
            status["session_duration"] = time.time() - self.session_start_time
        
        return status
    
    def cleanup(self) -> None:
        """Cleanup resources."""
        try:
            self.stop_session()
            
            if self.adb:
                self.adb.disconnect()
            
            if self.image_handler:
                self.image_handler.clear_cache()
            
            logger.info("Bot cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup()
